#!/usr/bin/env python3
"""
Integration test for Agno Workflow implementation.

This script tests the new Agno workflow integration to ensure it works
properly before full deployment.
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from complaint_classifier.classifier import ComplaintClassifier
from complaint_classifier.config import Settings

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_agno_workflow():
    """Test the Agno workflow implementation."""
    print("🧪 Testing Agno Workflow Integration")
    print("=" * 50)
    
    # Test complaint text
    test_complaint = "路燈不亮了，請派人修理，這已經影響晚上的交通安全"
    
    try:
        # Test with Agno workflow
        print("\n🔄 Testing with Agno Workflow...")
        os.environ["USE_AGNO_WORKFLOW"] = "true"
        
        agno_classifier = ComplaintClassifier(setup_logs=False)
        agno_result = await agno_classifier.classify(
            content=test_complaint,
            case_id="TEST_AGNO_001"
        )
        
        print(f"✅ Agno Result: {agno_result.category.value}")
        print(f"   Main Category: {agno_result.main_category.value}")
        print(f"   Confidence: {agno_result.confidence:.3f}")
        print(f"   Path: {agno_result.path_taken}")
        
        # Test with legacy workflow for comparison
        print("\n🔄 Testing with Legacy Workflow...")
        os.environ["USE_AGNO_WORKFLOW"] = "false"
        
        legacy_classifier = ComplaintClassifier(setup_logs=False)
        legacy_result = await legacy_classifier.classify(
            content=test_complaint,
            case_id="TEST_LEGACY_001"
        )
        
        print(f"✅ Legacy Result: {legacy_result.category.value}")
        print(f"   Main Category: {legacy_result.main_category.value}")
        print(f"   Confidence: {legacy_result.confidence:.3f}")
        print(f"   Path: {legacy_result.path_taken}")
        
        # Compare results
        print("\n📊 Comparison:")
        print(f"   Categories Match: {agno_result.category == legacy_result.category}")
        print(f"   Main Categories Match: {agno_result.main_category == legacy_result.main_category}")
        
        # Test system validation
        print("\n🔍 Testing System Validation...")
        agno_validation = await agno_classifier.validate_setup()
        legacy_validation = await legacy_classifier.validate_setup()
        
        print(f"   Agno Validation: {agno_validation}")
        print(f"   Legacy Validation: {legacy_validation}")
        
        print("\n✨ Integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_error_handling():
    """Test error handling in Agno workflow."""
    print("\n🧪 Testing Error Handling")
    print("=" * 30)
    
    try:
        os.environ["USE_AGNO_WORKFLOW"] = "true"
        classifier = ComplaintClassifier(setup_logs=False)
        
        # Test with empty content
        result = await classifier.classify(
            content="",
            case_id="TEST_ERROR_001"
        )
        
        print(f"✅ Empty content handled: {result.category.value}")
        
        # Test with very long content
        long_content = "路燈問題 " * 1000
        result = await classifier.classify(
            content=long_content,
            case_id="TEST_ERROR_002"
        )
        
        print(f"✅ Long content handled: {result.category.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False


async def main():
    """Run all integration tests."""
    print("🚀 Starting Agno Workflow Integration Tests")
    print("=" * 60)
    
    # Run basic integration test
    basic_test_passed = await test_agno_workflow()
    
    # Run error handling test
    error_test_passed = await test_error_handling()
    
    # Summary
    print("\n📋 Test Summary:")
    print(f"   Basic Integration: {'✅ PASS' if basic_test_passed else '❌ FAIL'}")
    print(f"   Error Handling: {'✅ PASS' if error_test_passed else '❌ FAIL'}")
    
    if basic_test_passed and error_test_passed:
        print("\n🎉 All tests passed! Agno workflow integration is ready.")
        return 0
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)