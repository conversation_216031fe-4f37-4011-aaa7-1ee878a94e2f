#!/usr/bin/env python3
"""Debug script to test agno response metrics structure."""

import asyncio
import os
import json

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from dotenv import load_dotenv

load_dotenv()


async def test_agno_metrics():
    """Test agno response metrics structure."""
    try:
        # Create a simple agent
        model = OpenAIChat(
            id="gpt-4o-mini",
            temperature=0.1,
            api_key=os.getenv("OPENAI_API_KEY")
        )
        
        agent = Agent(
            name="TestAgent",
            model=model,
            instructions=[
                "You are a helpful assistant. Always respond with valid JSON."
            ],
            telemetry=False,
            monitoring=False,
        )

        # Test with a simple prompt
        prompt = 'Please respond with this JSON: {"test": "hello", "status": "ok"}'
        response = await agent.arun(prompt)

        print("=== RESPONSE STRUCTURE ===")
        print(f"Response type: {type(response)}")
        print(f"\nResponse attributes:")
        for attr in sorted(dir(response)):
            if not attr.startswith('_'):
                try:
                    value = getattr(response, attr)
                    if not callable(value):
                        print(f"  {attr}: {type(value).__name__}")
                        if attr in ['content', 'metrics', 'messages', 'usage']:
                            print(f"    Value: {value}")
                except Exception as e:
                    print(f"  {attr}: <error accessing: {e}>")

        # Check specific attributes
        print("\n=== CHECKING FOR METRICS ===")
        if hasattr(response, 'metrics'):
            print(f"Has metrics: {response.metrics}")
        
        if hasattr(response, 'usage'):
            print(f"Has usage: {response.usage}")
            
        if hasattr(response, 'model_usage'):
            print(f"Has model_usage: {response.model_usage}")

        # Check agent's last_response
        print("\n=== AGENT LAST RESPONSE ===")
        if hasattr(agent, 'last_response'):
            print(f"Agent has last_response: {type(agent.last_response)}")
            if agent.last_response:
                for attr in sorted(dir(agent.last_response)):
                    if not attr.startswith('_'):
                        try:
                            value = getattr(agent.last_response, attr)
                            if not callable(value) and attr in ['metrics', 'usage', 'model_usage']:
                                print(f"  {attr}: {value}")
                        except:
                            pass

        # Check the raw model response
        print("\n=== RAW MODEL RESPONSE ===")
        if hasattr(model, '_client'):
            print("Model has _client")
            
        # Try calling the model directly
        from agno.utils import Message
        messages = [Message(role="user", content=prompt)]
        
        print("\n=== DIRECT MODEL CALL ===")
        model_response = await model.arun(messages)
        print(f"Model response type: {type(model_response)}")
        if hasattr(model_response, 'usage'):
            print(f"Model response usage: {model_response.usage}")
        if hasattr(model_response, 'metrics'):
            print(f"Model response metrics: {model_response.metrics}")

        return response

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_agno_metrics())