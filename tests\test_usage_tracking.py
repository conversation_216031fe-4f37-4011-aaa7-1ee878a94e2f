#!/usr/bin/env python3
"""
Test script for the usage tracking functionality.
"""

import logging

from src.complaint_classifier.usage.batch_reporter import BatchReporter
from src.complaint_classifier.usage.tracker import (
    CostCalculator,
    ModelProvider,
    TokenUsage,
    UsageTracker,
)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_cost_calculator():
    """Test the cost calculator functionality."""
    print("=" * 50)
    print("🧮 Testing Cost Calculator")
    print("=" * 50)

    calculator = CostCalculator()

    # Test with sample usage
    usage = TokenUsage(
        model="gpt-4.1-mini",
        provider=ModelProvider.OPENAI,
        input_tokens=1500,
        output_tokens=800,
        total_tokens=2300,
    )

    # Calculate cost
    usage_with_cost = calculator.calculate_cost(usage)

    print(f"Model: {usage_with_cost.model}")
    print(f"Input tokens: {usage_with_cost.input_tokens:,}")
    print(f"Output tokens: {usage_with_cost.output_tokens:,}")
    print(f"Total tokens: {usage_with_cost.total_tokens:,}")
    print(f"Input cost: ${usage_with_cost.input_cost:.6f}")
    print(f"Output cost: ${usage_with_cost.output_cost:.6f}")
    print(f"Total cost: ${usage_with_cost.total_cost:.6f}")
    print()


def test_usage_tracker():
    """Test the usage tracker functionality."""
    print("=" * 50)
    print("📊 Testing Usage Tracker")
    print("=" * 50)

    tracker = UsageTracker()

    # Simulate some API calls
    metrics_samples = [
        {"input_tokens": 1200, "output_tokens": 450, "total_tokens": 1650, "time": 2.1},
        {"input_tokens": 800, "output_tokens": 320, "total_tokens": 1120, "time": 1.5},
        {"input_tokens": 2000, "output_tokens": 750, "total_tokens": 2750, "time": 3.2},
    ]

    for i, metrics in enumerate(metrics_samples, 1):
        usage = tracker.track_usage("gpt-4o-mini", "openai", metrics)
        print(f"Call {i}: {usage.total_tokens} tokens, ${usage.total_cost:.6f} USD")

    # Get summary
    summary = tracker.get_session_summary()
    print("\nSession Summary:")
    print(summary.get_formatted_summary())


def test_batch_reporter():
    """Test the batch reporter functionality."""
    print("=" * 50)
    print("📋 Testing Batch Reporter")
    print("=" * 50)

    # Create a custom usage tracker for this test
    tracker = UsageTracker()
    reporter = BatchReporter(tracker)

    # Start batch
    reporter.start_batch()

    # Simulate some classification results
    test_cases = [
        {
            "case_id": "CASE001",
            "content": "路燈不亮了，請派人修理",
            "category": "路燈故障",
            "main_category": "路燈、路樹及公園管理維護",
            "metrics": {
                "input_tokens": 800,
                "output_tokens": 300,
                "total_tokens": 1100,
            },
        },
        {
            "case_id": "CASE002",
            "content": "公車站牌倒了需要修理",
            "category": "公車問題及站牌、候車亭設施管理",
            "main_category": "交通號誌、標誌、標線及大眾運輸",
            "metrics": {
                "input_tokens": 900,
                "output_tokens": 400,
                "total_tokens": 1300,
            },
        },
        {
            "case_id": "CASE003",
            "content": "垃圾車沒有來收垃圾",
            "category": "垃圾車清運動線及管理",
            "main_category": "噪音、污染及環境維護",
            "metrics": {
                "input_tokens": 750,
                "output_tokens": 250,
                "total_tokens": 1000,
            },
        },
    ]

    # Mock result class
    class MockResult:
        def __init__(self, category, main_category):
            self.category = type("obj", (object,), {"value": category})
            self.main_category = type("obj", (object,), {"value": main_category})
            self.confidence = 0.85

    for case in test_cases:
        # Track usage
        tracker.track_usage("gpt-4o-mini", "openai", case["metrics"])

        # Add to batch
        result = MockResult(case["category"], case["main_category"])
        reporter.add_classification_result(
            case["case_id"], case["content"], result, processing_time=2.5
        )

    # End batch and get summary
    batch_summary = reporter.end_batch()

    print(f"Processed {batch_summary['batch_info']['total_cases']} cases")
    print(f"Total cost: ${batch_summary['usage_summary']['total_cost_usd']} USD")

    # Generate cost analysis report
    cost_report = reporter.generate_cost_analysis_report(batch_summary)
    print("\nCost Analysis Report:")
    print(cost_report)

    # Test export functions
    try:
        json_file = reporter.export_to_json("test_batch_results.json", batch_summary)
        print(f"\n✅ JSON export successful: {json_file}")

        csv_file = reporter.export_to_csv("test_batch_results.csv", batch_summary)
        print(f"✅ CSV export successful: {csv_file}")

    except Exception as e:
        print(f"❌ Export failed: {e}")


def test_integration():
    """Test integration with the complaint classifier (mock)."""
    print("=" * 50)
    print("🔄 Testing Integration")
    print("=" * 50)

    from src.complaint_classifier.models import UsageSummaryInfo
    from src.complaint_classifier.usage.tracker import get_usage_tracker

    # Get global tracker
    tracker = get_usage_tracker()

    # Reset for clean test
    tracker.reset_session()

    # Simulate multiple classifications
    test_metrics = [
        {"input_tokens": 1200, "output_tokens": 500, "total_tokens": 1700},
        {"input_tokens": 950, "output_tokens": 380, "total_tokens": 1330},
        {"input_tokens": 1100, "output_tokens": 450, "total_tokens": 1550},
    ]

    for i, metrics in enumerate(test_metrics, 1):
        usage = tracker.track_usage("gpt-4o-mini", "openai", metrics)
        print(
            f"Classification {i}: {usage.total_tokens} tokens, ${usage.total_cost:.6f} USD"
        )

    # Test UsageSummaryInfo creation
    summary = tracker.get_session_summary()
    usage_info = UsageSummaryInfo(
        total_requests=summary.total_requests,
        total_input_tokens=summary.total_input_tokens,
        total_output_tokens=summary.total_output_tokens,
        total_tokens=summary.total_tokens,
        total_cost=summary.total_cost,
        model_breakdown=summary.model_breakdown,
    )

    print("\nUsage Summary:")
    print(usage_info.get_summary_text())


def main():
    """Run all tests."""
    print("🚀 Starting Usage Tracking System Tests")
    print()

    try:
        test_cost_calculator()
        test_usage_tracker()
        test_batch_reporter()
        test_integration()

        print("✅ All tests completed successfully!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
