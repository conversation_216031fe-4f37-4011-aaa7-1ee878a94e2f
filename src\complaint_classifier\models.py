"""
Pydantic models for the complaint classification system.

This module defines the data structures used throughout the system
for type safety and validation.
"""

from enum import Enum
from typing import Any, Dict, List, Literal, Optional
from decimal import Decimal

from pydantic import BaseModel, Field


class ActionType(str, Enum):
    """Action types for agent decisions."""

    FINALIZE = "finalize"
    ESCALATE = "escalate"


class DecisionType(str, Enum):
    """Review decision types."""

    PASS = "pass"
    FAIL = "fail"


class MainCategory(str, Enum):
    """Main categories for complaint classification."""

    TRAFFIC_SIGNALS_SIGNS_LINES_TRANSPORT = "交通號誌、標誌、標線及大眾運輸"
    ROAD_OBSTRUCTION_REMOVAL = "路霸排除"
    NOISE_POLLUTION_ENVIRONMENT = "噪音、污染及環境維護"
    OTHER_CATEGORY = "其他類別"
    POLICE_TRAFFIC_ENFORCEMENT = "警政及交通裁罰業務"
    BUILDING_MANAGEMENT = "建築管理"
    ROAD_DRAINAGE_MAINTENANCE = "道路、水溝維護"
    STREET_LIGHTS_TREES_PARKS = "路燈、路樹及公園管理維護"
    HEALTH_ADMINISTRATION = "衛生行政"
    EDUCATION_SPORTS = "教育及體育"
    LABOR_ADMINISTRATION = "勞動行政"
    COMMERCE_ECONOMY_TAX = "工商、經濟及稅務"
    SOCIAL_ASSISTANCE_WELFARE = "社會救助及社會福利"
    LAND_ADMINISTRATION = "地政服務"
    FIRE_ADMINISTRATION = "消防行政"
    SERVICE_QUALITY_WEBSITE_APP = "感謝函、服務品質及網站、APP管理問題"
    ANIMAL_SHELTER_PROTECTION = "動物收容、保護及捕捉"
    CULTURE_ARTS_LIBRARY = "文化藝術及圖書管理"
    CIVIL_AFFAIRS = "民政業務"
    POLITICAL_ETHICS = "政風行政"
    CITIZEN_CARD = "市民卡業務"


class SubCategory(str, Enum):
    """Sub categories for complaint classification."""

    # 交通號誌、標誌、標線及大眾運輸
    BUS_PROBLEMS_STOP_FACILITIES = "公車問題及站牌、候車亭設施管理"
    TRAFFIC_SIGNS_LINES_MIRRORS = "交通標誌、標線、反射鏡設置或移除"
    TRAFFIC_SIGNALS_SIGNS_LINES_TRANSPORT_OTHER = "交通號誌、標誌、標線及大眾運輸_其他"
    TRAFFIC_SIGNAL_INSTALLATION_TIMING = "交通號誌增設或紅綠燈秒數調整"
    ROADSIDE_PARKING_ISSUES = "路邊停車格問題"
    UBIKE_RENTAL_ISSUES = "公共自行車(U-Bike)租賃問題"
    TRAFFIC_SIGNAL_MALFUNCTION = "交通號誌(紅綠燈)故障或損壞傾斜"
    TRAFFIC_SIGN_MIRROR_DAMAGE = "交通標誌牌面、反射鏡損壞傾斜"
    MRT_OPERATION_MANAGEMENT = "捷運營運及管理"
    MRT_CONSTRUCTION_ISSUES = "捷運建設工程相關問題"
    BUS_DYNAMIC_SYSTEM_ISSUES = "公車動態系統問題"
    PARKING_FEE_ISSUES = "停車費問題"
    TAXI_ISSUES_STAND_FACILITIES = "計程車問題及招呼站設施管理"
    FREE_CITIZEN_BUS_ISSUES = "免費市民公車(樂活巴)問題"

    # 路霸排除
    ROAD_ARCADE_SIDEWALK_OCCUPATION = "占用道路、騎樓及人行道"
    UNLICENSED_ABANDONED_VEHICLE_REPORT = "無牌廢棄車查報"
    LICENSED_ABANDONED_VEHICLE_REPORT = "有牌廢棄車查報"
    ROAD_OBSTRUCTION_REMOVAL_OTHER = "路霸排除_其他"
    ADVERTISING_VEHICLE_PARKING_OCCUPATION = "廣告車輛長期占用停車格"

    # 噪音、污染及環境維護
    DIRTY_SPOT_REPORT = "髒亂點查報"
    RESIDENTIAL_VEHICLE_NOISE = "住家、改裝車噪音"
    COMPREHENSIVE_ENVIRONMENTAL_POLLUTION = "綜合性環境污染"
    NOISE_POLLUTION_ENVIRONMENT_OTHER = "噪音、污染及環境維護_其他"
    BUSINESS_FACTORY_CONSTRUCTION_NOISE = "營業場所、工廠及施工噪音"
    AIR_POLLUTION = "空氣污染"
    RESIDENTIAL_HUMAN_ANIMAL_NOISE = "住宅內人與動物噪音"
    GARBAGE_TRUCK_ROUTE_MANAGEMENT = "垃圾車清運動線及管理"
    FACTORY_WASTEWATER_RIVER_POLLUTION = "工廠排放廢水、河川污染"
    ILLEGAL_ADVERTISING_POSTING = "違規張貼廣告物"
    WASTE_COLLECTION_APPOINTMENT = "廢棄物清運預約"
    DOG_CAT_CARCASS_REMOVAL = "犬貓屍體清除"

    # 其他類別
    OTHER_SUGGESTIONS_CONSULTATIONS_COMPLAINTS = "其他建議、諮詢或陳情"
    OTHER_REPORT_CASES = "其他檢舉案件"

    # 警政及交通裁罰業務
    POLICE_TRAFFIC_ENFORCEMENT_OTHER = "警政及交通裁罰業務_其他"
    POLICE_DISCIPLINE = "警政風紀"
    TRAFFIC_TICKET_APPEAL = "交通罰單申訴"
    TRAFFIC_GUIDANCE_CONGESTION_REPORT = "交通疏導或壅塞通報"
    SECURITY_MAINTENANCE = "治安維護"
    MORALITY_VIOLATION = "妨害風化(俗)"
    RED_LIGHT_SPEED_CAMERA_INSTALLATION = "闖紅燈(超速)照相桿增設或維護"
    SURVEILLANCE_CAMERA_ISSUES = "監視器問題"
    VEHICLE_TOWING_DISPUTE = "車輛拖吊爭議"

    # 建築管理
    APARTMENT_BUILDING_MANAGEMENT_ISSUES = "公寓大廈管理問題"
    GENERAL_ILLEGAL_CONSTRUCTION_REPORT = "一般違建查報"
    BUILDING_PUBLIC_SAFETY_ISSUES = "建築物公共安全問題"
    BUILDING_MANAGEMENT_OTHER = "建築管理_其他"
    UNDER_CONSTRUCTION_ILLEGAL_BUILDING_REPORT = "興建中違建查報"
    BUILDING_REGULATION_ISSUES = "建築法規問題"
    ILLEGAL_SIGNAGE_ADVERTISING_REPORT = "違規招牌或樹立廣告物查報"
    SOCIAL_HOUSING_MANAGEMENT = "社會住宅管理"
    LICENSED_CONSTRUCTION_NEIGHBOR_DAMAGE = "領有建造執照施工損鄰"
    REPORTED_ILLEGAL_BUILDING_DEMOLITION = "已查報違建拆除問題"

    # 道路、水溝維護
    ROAD_SURFACE_UNEVEN_HOLES = "路面不平整或掏空破洞"
    ROAD_DRAINAGE_MAINTENANCE_OTHER = "道路、水溝維護_其他"
    ROAD_CONSTRUCTION_TIME_TRAFFIC_SAFETY = "道路施工時間、交通管制及安全管理問題"
    ROAD_SIDE_DITCH_CLEANING_ODOR = "道路側溝清淤或惡臭處理"
    DRAINAGE_COVER_REPAIR = "水溝溝蓋維修"
    ROAD_FLOODING_REPORT = "道路淹(積)水通報"
    MANHOLE_COVER_NOISE = "孔蓋異音"
    CABLE_UNDERGROUND_HANGING = "電纜下地或纜線垂落"
    ROAD_OIL_STAIN_REMOVAL = "路面油漬清除"

    # 路燈、路樹及公園管理維護
    STREET_LIGHTS_TREES_PARKS_OTHER = "路燈、路樹及公園管理維護_其他"
    PARK_GREENERY_TREE_MAINTENANCE = "公園、綠地及路樹養護"
    STREET_LIGHT_MALFUNCTION = "路燈故障"
    PARK_FACILITY_DAMAGE = "公園設施損壞"
    TREE_FALLING = "路樹傾倒"
    STREET_LIGHT_INSTALLATION_RELOCATION = "路燈新增或遷移申請"
    NEW_PARK_PROPOSAL = "新闢公園建議案"

    # 衛生行政
    FOOD_SAFETY_HYGIENE = "食品安全衛生"
    DRUG_COSMETIC_MANAGEMENT = "藥品及化妝品管理"
    MEDICAL_MANAGEMENT = "醫療管理"
    TOBACCO_CONTROL = "菸害防制"
    HEALTH_ADMINISTRATION_OTHER = "衛生行政_其他"
    INFECTIOUS_DISEASE_PREVENTION_VACCINATION = "傳染病防治及預防接種"
    SUICIDE_PREVENTION_MENTAL_HEALTH = "自殺防治及心理健康"

    # 教育及體育
    ELEMENTARY_SCHOOL_ISSUES = "國小學校問題"
    JUNIOR_HIGH_SCHOOL_ISSUES = "國中學校問題"
    EDUCATION_SPORTS_OTHER = "教育及體育_其他"
    SENIOR_HIGH_SCHOOL_ISSUES = "高級中等學校問題"
    CRAM_SCHOOL_ISSUES = "補教問題"
    SPORTS_ACTIVITY_VENUE_MANAGEMENT = "體育活動及場務管理"
    KINDERGARTEN_ISSUES = "幼兒園問題"
    SPECIAL_EDUCATION_ISSUES = "特殊教育問題"
    TEACHER_TRANSFER_RECRUITMENT = "教師介聘甄選"
    COMMUNITY_COLLEGE_LIFELONG_EDUCATION = "社區大學、樂齡學習等終身教育問題"
    SCHOOL_SPORTS_ISSUES = "學校體育問題"

    # 勞動行政
    COMPANY_EMPLOYER_LABOR_LAW_VIOLATION = "檢舉公司(雇主)違反勞動法規"
    LABOR_LAW_CONSULTATION = "勞工法令諮詢"
    LABOR_ADMINISTRATION_OTHER = "勞動行政_其他"
    LABOR_DISPUTE_COORDINATION = "勞資糾紛協調"
    MIGRANT_WORKER_AFFAIRS = "移工業務"
    EMPLOYMENT_SERVICE_VOCATIONAL_TRAINING = "就業服務及職業訓練"
    EMPLOYMENT_DISCRIMINATION = "就業歧視"
    DISABLED_EMPLOYMENT = "身障就業"

    # 工商、經濟及稅務
    STORE_ILLEGAL_OPERATION_REPORT = "檢舉商店違規營業"
    UTILITY_ISSUES = "水、電、瓦斯等公用事業問題"
    TAX_ISSUES = "稅務問題"
    COMMERCE_ECONOMY_TAX_OTHER = "工商、經濟及稅務_其他"
    FACTORY_ILLEGAL_OPERATION_REPORT = "檢舉工廠違規營業"
    MARKET_VENDOR_MANAGEMENT = "市場攤販管理"
    BUSINESS_REGISTRATION_ISSUES = "工商登記問題"
    HOTEL_HOMESTAY_ILLEGAL_OPERATION_REPORT = "檢舉旅館、民宿違規營業"

    # 社會救助及社會福利
    SOCIAL_ASSISTANCE_WELFARE_OTHER = "社會救助及社會福利_其他"
    DISABILITY_WELFARE_REHABILITATION_BUS = "身心障礙福利及復康巴士"
    ELDERLY_WELFARE_LONG_TERM_DAY_CARE = "銀髮族福利、長期照顧及日間照顧"
    SOCIAL_ASSISTANCE_LOW_INCOME_EMERGENCY = (
        "社會救助(中、低收入戶、急難救助及馬上關懷等)"
    )
    WOMEN_WELFARE_SPECIAL_CIRCUMSTANCES_BIRTH_CHILDCARE = (
        "婦女福利、特殊境遇家庭扶助、生育津貼及育兒津貼"
    )
    WOMEN_CHILDREN_CENTER_CHILDCARE_CENTER = "婦女(幼)館、親子館及公設民營托嬰中心管理"
    CHILDREN_YOUTH_WELFARE_EARLY_INTERVENTION_ASSISTANCE = (
        "兒少福利、兒童早療補助、弱勢兒少生活扶助、緊急生活扶助及醫療補助"
    )
    DOMESTIC_VIOLENCE_SEXUAL_ASSAULT_CHILD_PROTECTION = (
        "家庭暴力、性侵害、兒少保護及性騷擾等防治工作"
    )
    HOUSING_RENTAL_SUBSIDY_ISSUES = "住宅租金補貼問題"
    PEOPLE_ORGANIZATION_GUIDANCE = "人民團體組織輔導"
    FAMILY_SERVICE_CENTER = "家庭服務中心"

    # 地政服務
    LAND_ILLEGAL_USE_REPORT = "檢舉土地違規使用"
    LAND_ADMINISTRATION_OTHER = "地政服務_其他"
    REAL_ESTATE_TRANSACTION = "不動產交易"
    LAND_EXPROPRIATION = "土地徵收"
    LAND_BUILDING_REGISTRATION = "土地及建物登記"
    LAND_REZONING = "土地重劃"
    LAND_SURVEYING = "土地測量"
    CADASTRAL_MAP_RESURVEY = "地籍圖重測"

    # 消防行政
    FIRE_EQUIPMENT_SAFETY_INSPECTION = "消防設備、安全檢查"
    FIRE_LANE_ILLEGAL_CONSTRUCTION_DEBRIS = "防火巷違建、堆放雜物"
    FIRE_ADMINISTRATION_OTHER = "消防行政_其他"
    GAS_CYLINDER_STORAGE_ISSUES = "瓦斯桶儲放問題"
    FIRE_HYDRANT_INSTALLATION_RELOCATION_SIGNAGE = "消防栓(設置、移位、告示牌)"

    # 感謝函、服務品質及網站、APP管理問題
    THANK_YOU_LETTER = "感謝函"
    GOVERNMENT_WEBSITE_APP_MANAGEMENT = "市府網站或APP管理問題"
    SERVICE_ATTITUDE_ISSUES = "服務態度問題"
    ADMINISTRATIVE_EFFICIENCY_ISSUES = "行政效率問題"
    PROFESSIONAL_KNOWLEDGE_ISSUES = "專業知識問題"
    SERVICE_QUALITY_WEBSITE_APP_OTHER = "感謝函、服務品質及網站、APP管理問題_其他"

    # 動物收容、保護及捕捉
    ANIMAL_SHELTER_PROTECTION_OTHER = "動物收容、保護及捕捉_其他"
    ANIMAL_SHELTER_ADOPTION_ISSUES = "動物收容及認養問題"
    ANIMAL_TRAPPED_INJURED_REPORT = "動物受困、受傷通報"
    BEE_SNAKE_CATCHING = "捕蜂、抓蛇"

    # 文化藝術及圖書管理
    LIBRARY_READING_ROOM_VENUE_MANAGEMENT = "圖書館、閱覽室及館舍管理"
    ARTS_PERFORMANCE_ACTIVITIES = "藝文展演活動"
    CULTURE_ARTS_LIBRARY_OTHER = "文化藝術及圖書管理_其他"
    ARTS_VENUE_MANAGEMENT = "藝文館舍管理"
    CULTURAL_HERITAGE_ISSUES = "文化資產問題"

    # 民政業務
    HOUSEHOLD_REGISTRATION_SERVICE = "戶政服務"
    CIVIL_AFFAIRS_OTHER = "民政業務_其他"
    RELIGIOUS_AFFAIRS = "宗教事務"
    FUNERAL_CEREMONY = "殯葬禮儀"
    MILITARY_SERVICE_ISSUES = "兵役問題"

    # 政風行政
    ADMINISTRATIVE_MISCONDUCT = "行政違失"
    OTHER_MALFEASANCE = "其他瀆職情形"
    BRIBERY_CORRUPTION = "行、收賄"

    # 市民卡業務
    CITIZEN_CARD_OTHER = "市民卡業務_其他"
    CITIZEN_CARD_BENEFITS_VALUE_ADDED_SERVICES = "市民卡優惠及加值服務建議"
    CARD_SENSING_USAGE_ISSUES = "卡片感應及使用問題"
    STUDENT_CARD_APPLICATION_ISSUES = "學生卡申辦問題"
    GENERAL_CARD_APPLICATION_ISSUES = "一般卡申辦問題"
    MOBILE_CARD_JOINT_CARD_APPLICATION = "行動卡、聯名卡申辦問題"
    VOLUNTEER_CARD_APPLICATION_ISSUES = "志工卡申辦問題"
    SENIOR_CARD_APPLICATION_ISSUES = "敬老卡申辦問題"


class TriageResult(BaseModel):
    """Result from the Triage Agent."""

    action: ActionType
    category: Optional[SubCategory] = None
    confidence: float = Field(ge=0.0, le=1.0)
    reasoning: str


class MainCategoryCandidate(BaseModel):
    """A main category candidate with confidence and reasoning."""

    category: MainCategory
    confidence: float = Field(ge=0.0, le=1.0)
    reasoning: str
    keywords_found: Optional[List[str]] = None
    evidence_from_text: Optional[str] = None


class SubCategoryCandidate(BaseModel):
    """A sub category candidate with confidence and reasoning."""

    category: SubCategory
    confidence: float = Field(ge=0.0, le=1.0)
    reasoning: str
    keywords_found: Optional[List[str]] = None
    evidence_from_text: Optional[str] = None


class MainCategoryResult(BaseModel):
    """Result from Main Category Analyzer."""

    candidates: List[MainCategoryCandidate] = Field(min_length=1, max_length=3)


class SubCategoryResult(BaseModel):
    """Result from Sub Category Analyzer."""

    parent_category: MainCategory
    candidates: List[SubCategoryCandidate] = Field(min_length=1, max_length=3)


class ReviewResult(BaseModel):
    """Result from Review Agent."""

    decision: DecisionType
    justification: str
    recommendation: Optional[str] = None


class UsageSummaryInfo(BaseModel):
    """Summary of token usage and costs for a classification request."""
    
    total_requests: int = 0
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    total_tokens: int = 0
    total_cost: Decimal = Field(default_factory=lambda: Decimal('0'))
    
    # Model breakdown for multiple agents
    model_breakdown: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    
    def get_summary_text(self) -> str:
        """Get formatted summary text."""
        if self.total_requests == 0:
            return "無使用統計"
        
        summary = (
            f"🤖 Token 使用: 輸入 {self.total_input_tokens:,} + "
            f"輸出 {self.total_output_tokens:,} = 總計 {self.total_tokens:,} tokens | "
            f"費用: ${self.total_cost:.6f} USD"
        )
        
        if len(self.model_breakdown) > 1:
            breakdown_parts = []
            for model, stats in self.model_breakdown.items():
                breakdown_parts.append(f"{model}: {stats['total_tokens']:,} tokens")
            summary += f" ({', '.join(breakdown_parts)})"
        
        return summary


class ClassificationResult(BaseModel):
    """Final classification result."""

    category: SubCategory
    main_category: MainCategory
    confidence: Optional[float] = None
    path_taken: Literal["fast_track", "expert_review"] = "expert_review"
    reasoning_chain: List[Dict[str, Any]] = Field(default_factory=list)
    review_passed: Optional[bool] = None
    main_category_reasoning: str = Field(
        default="",
        description="Reasoning for main category classification"
    )
    sub_category_reasoning: str = Field(
        default="",
        description="Reasoning for sub-category classification"
    )
    classification_summary: str = Field(
        default="",
        description="Summary of the classification reasoning"
    )
    usage_summary: Optional[UsageSummaryInfo] = Field(
        default=None,
        description="Token usage and cost summary for this classification"
    )


class ComplaintCase(BaseModel):
    """Input complaint case."""

    content: str
    case_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
