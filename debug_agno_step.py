#!/usr/bin/env python3
"""
Debug script for Agno workflow step execution.
"""

import asyncio
import logging
from src.complaint_classifier.models import ComplaintCase
from src.complaint_classifier.workflow.agno_workflow import ComplaintClassificationWorkflow
from src.complaint_classifier.config import Settings
from agno.workflow.v2 import StepInput

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_workflow_step():
    """Test a single workflow step."""
    try:
        # Initialize workflow
        settings = Settings()
        workflow = ComplaintClassificationWorkflow(settings)
        
        # Create test complaint
        complaint = ComplaintCase(
            case_id="DEBUG_001",
            content="路燈不亮了，請派人修理",
            source="debug_test"
        )
        
        # Create StepInput
        step_input = StepInput(
            message=f"Classify complaint: {complaint.content}"
        )
        
        # Check StepInput attributes
        logger.info(f"StepInput attributes: {dir(step_input)}")
        logger.info(f"StepInput message: {step_input.message}")
        
        # Test main category step
        logger.info("Testing main category step...")
        result = await workflow._main_category_step(step_input)
        logger.info(f"Result: {result}")
        
    except Exception as e:
        logger.error(f"Error in step test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_workflow_step())