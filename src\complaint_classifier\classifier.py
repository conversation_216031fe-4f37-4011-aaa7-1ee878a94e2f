"""
Main complaint classifier interface.
"""

import logging
import os
from typing import Any, Dict, Optional

from .config import Settings
from .logging_config import setup_logging
from .models import ClassificationResult, ComplaintCase, UsageSummaryInfo
from .usage.tracker import get_usage_tracker
from .workflow import WorkflowEngine
from .workflow.agno_workflow import ComplaintClassificationWorkflow

logger = logging.getLogger(__name__)


class ComplaintClassifier:
    """
    Main interface for the AI Multi-Agent Complaint Classification System.

    This class provides a simple interface for classifying citizen complaints
    using the expert committee approach with multiple specialized AI agents.
    """

    def __init__(self, settings: Optional[Settings] = None, setup_logs: bool = True):
        """Initialize the complaint classifier.

        Args:
            settings: Application settings. If None, default settings will be used.
            setup_logs: Whether to setup logging configuration. Set to False if already configured.
        """
        # Setup logging if requested
        if setup_logs:
            setup_logging(level="INFO")

        self.settings = settings or Settings()

        # Feature flag for workflow selection
        self.use_agno_workflow = (
            os.getenv("USE_AGNO_WORKFLOW", "true").lower() == "true"
        )

        if self.use_agno_workflow:
            logger.info("Initializing with Agno Workflow")
            self.agno_workflow = ComplaintClassificationWorkflow(self.settings)
            self.workflow_engine = None
        else:
            logger.info("Initializing with legacy WorkflowEngine")
            self.workflow_engine = WorkflowEngine(self.settings)
            self.agno_workflow = None

        logger.info(
            f"ComplaintClassifier initialized (workflow: {'Agno' if self.use_agno_workflow else 'Legacy'})"
        )

    async def classify(
        self,
        content: str,
        case_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> ClassificationResult:
        """Classify a citizen complaint.

        Args:
            content: The complaint text content
            case_id: Optional case identifier
            metadata: Optional metadata dictionary

        Returns:
            Classification result with category and reasoning
        """
        # Create complaint case
        complaint = ComplaintCase(content=content, case_id=case_id, metadata=metadata)

        logger.info(f"Classifying complaint: {case_id or 'unnamed'}")

        # Get tracker and record usage before processing
        tracker = get_usage_tracker()
        initial_summary = tracker.get_session_summary()

        # Record initial values
        initial_requests = initial_summary.total_requests
        initial_input_tokens = initial_summary.total_input_tokens
        initial_output_tokens = initial_summary.total_output_tokens
        initial_tokens = initial_summary.total_tokens
        initial_cost = initial_summary.total_cost
        initial_breakdown = dict(initial_summary.model_breakdown)

        # Process through appropriate workflow
        if self.use_agno_workflow:
            result = await self.agno_workflow.classify_complaint(complaint)
        else:
            result = await self.workflow_engine.classify_complaint(complaint)

        # Calculate usage for this classification
        final_summary = tracker.get_session_summary()

        # Create usage summary for this specific classification
        logger.debug(
            f"Initial requests: {initial_requests}, Final requests: {final_summary.total_requests}"
        )
        logger.debug(
            f"Initial tokens: {initial_tokens}, Final tokens: {final_summary.total_tokens}"
        )

        if final_summary.total_requests > initial_requests:
            usage_summary = UsageSummaryInfo(
                total_requests=final_summary.total_requests - initial_requests,
                total_input_tokens=final_summary.total_input_tokens
                - initial_input_tokens,
                total_output_tokens=final_summary.total_output_tokens
                - initial_output_tokens,
                total_tokens=final_summary.total_tokens - initial_tokens,
                total_cost=final_summary.total_cost - initial_cost,
                model_breakdown=self._calculate_model_breakdown_diff(
                    initial_breakdown, final_summary.model_breakdown
                ),
            )
            result.usage_summary = usage_summary

            # Log the usage information
            logger.info(
                f"Classification complete: {result.category} | {usage_summary.get_summary_text()}"
            )
        else:
            logger.warning(
                "No usage tracking detected - initial and final summaries are the same"
            )
            logger.info(f"Classification complete: {result.category}")

        return result

    def _calculate_model_breakdown_diff(
        self, initial: Dict[str, Dict[str, Any]], final: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """Calculate the difference in model breakdown between two summaries."""
        diff = {}

        for model, final_stats in final.items():
            initial_stats = initial.get(
                model,
                {
                    "requests": 0,
                    "input_tokens": 0,
                    "output_tokens": 0,
                    "total_tokens": 0,
                    "total_cost": 0,
                },
            )

            model_diff = {
                "requests": final_stats["requests"] - initial_stats["requests"],
                "input_tokens": final_stats["input_tokens"]
                - initial_stats["input_tokens"],
                "output_tokens": final_stats["output_tokens"]
                - initial_stats["output_tokens"],
                "total_tokens": final_stats["total_tokens"]
                - initial_stats["total_tokens"],
                "total_cost": final_stats["total_cost"] - initial_stats["total_cost"],
            }

            # Only include if there was actual usage
            if model_diff["requests"] > 0:
                diff[model] = model_diff

        return diff

    def get_available_categories(self) -> Dict[str, Dict[str, str]]:
        """Get all available categories from the knowledge base.

        Returns:
            Dictionary with main categories and their sub-categories
        """
        # Get knowledge manager from appropriate workflow
        knowledge_manager = (
            self.agno_workflow.knowledge_manager
            if self.use_agno_workflow
            else self.workflow_engine.knowledge_manager
        )

        kb = knowledge_manager.knowledge_base

        categories = {}
        for main_cat in kb.get_main_categories():
            categories[main_cat] = {
                "description": kb.get_main_category_description(main_cat),
                "sub_categories": knowledge_manager.get_sub_category_definitions(
                    main_cat
                ),
            }

        return categories

    def get_statistics(self) -> Dict[str, Any]:
        """Get system statistics.

        Returns:
            Dictionary with system statistics
        """
        # Get knowledge manager from appropriate workflow
        knowledge_manager = (
            self.agno_workflow.knowledge_manager
            if self.use_agno_workflow
            else self.workflow_engine.knowledge_manager
        )

        kb_stats = knowledge_manager.get_statistics()

        # Get usage statistics
        tracker = get_usage_tracker()
        usage_summary = tracker.get_session_summary()

        return {
            "workflow_type": "Agno" if self.use_agno_workflow else "Legacy",
            "knowledge_base": kb_stats,
            "configuration": {
                "fast_track_enabled": self.settings.workflow.enable_fast_track,
                "triage_threshold": self.settings.workflow.triage_confidence_threshold,
                "fallback_category": self.settings.workflow.fallback_category,
            },
            "usage_statistics": {
                "total_requests": usage_summary.total_requests,
                "total_input_tokens": usage_summary.total_input_tokens,
                "total_output_tokens": usage_summary.total_output_tokens,
                "total_tokens": usage_summary.total_tokens,
                "total_cost_usd": str(usage_summary.total_cost),
                "model_breakdown": usage_summary.model_breakdown,
            },
        }

    def get_usage_statistics(self) -> Dict[str, Any]:
        """Get detailed usage statistics.

        Returns:
            Dictionary with usage statistics
        """
        tracker = get_usage_tracker()
        summary = tracker.get_session_summary()

        return {
            "session_summary": {
                "total_requests": summary.total_requests,
                "total_input_tokens": summary.total_input_tokens,
                "total_output_tokens": summary.total_output_tokens,
                "total_tokens": summary.total_tokens,
                "total_cost_usd": str(summary.total_cost),
                "model_breakdown": summary.model_breakdown,
            },
            "formatted_summary": summary.get_formatted_summary(),
            "individual_records": [
                {
                    "model": record.model,
                    "provider": record.provider.value,
                    "input_tokens": record.input_tokens,
                    "output_tokens": record.output_tokens,
                    "total_tokens": record.total_tokens,
                    "input_cost": str(record.input_cost),
                    "output_cost": str(record.output_cost),
                    "total_cost": str(record.total_cost),
                    "processing_time": record.processing_time,
                }
                for record in summary.usage_records
            ],
        }

    def reset_usage_statistics(self):
        """Reset usage statistics for the current session."""
        tracker = get_usage_tracker()
        tracker.reset_session()
        logger.info("Usage statistics reset")

    async def validate_setup(self) -> Dict[str, bool]:
        """Validate that the system is properly set up.

        Returns:
            Dictionary with validation results
        """
        results = {}

        try:
            # Test knowledge base loading
            knowledge_manager = (
                self.agno_workflow.knowledge_manager
                if self.use_agno_workflow
                else self.workflow_engine.knowledge_manager
            )
            kb = knowledge_manager.knowledge_base
            results["knowledge_base"] = len(kb.categories) > 0
        except Exception as e:
            logger.error(f"Knowledge base validation failed: {e}")
            results["knowledge_base"] = False

        try:
            # Test agent initialization
            if self.use_agno_workflow:
                agents = [
                    self.agno_workflow.main_analyzer,
                    self.agno_workflow.sub_analyzer,
                    self.agno_workflow.review_agent,
                    self.agno_workflow.output_agent,
                ]
            else:
                agents = [
                    self.workflow_engine.triage_agent,
                    self.workflow_engine.main_analyzer,
                    self.workflow_engine.sub_analyzer,
                    self.workflow_engine.review_agent,
                    self.workflow_engine.output_agent,
                ]
            results["agents"] = all(agent is not None for agent in agents)
        except Exception as e:
            logger.error(f"Agent validation failed: {e}")
            results["agents"] = False

        try:
            # Test configuration
            results["configuration"] = (
                self.settings.openai_api_key is not None
                or self.settings.anthropic_api_key is not None
            )
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            results["configuration"] = False

        return results
