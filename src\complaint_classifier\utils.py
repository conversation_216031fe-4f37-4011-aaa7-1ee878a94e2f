"""
Utility functions for the complaint classification system.
"""

import json
import logging
import re
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


def parse_json_response(response_content: str) -> Optional[Dict[str, Any]]:
    """
    Parse JSON response from AI models, handling both plain JSON and markdown-wrapped JSON.

    Args:
        response_content: The raw response content from the AI model

    Returns:
        Parsed JSON data as dictionary, or None if parsing fails
    """
    if not response_content or not response_content.strip():
        logger.warning("Empty response content")
        return None

    # First, try to parse as plain JSON
    try:
        data = json.loads(response_content)
        return normalize_json_structure(data)
    except json.JSONDecodeError:
        # Try fixing formatting issues and parse again
        try:
            fixed_content = fix_json_formatting(response_content)
            data = json.loads(fixed_content)
            return normalize_json_structure(data)
        except json.JSONDecodeError:
            pass

    # If that fails, try to extract <PERSON>SO<PERSON> from markdown code blocks
    # Look for ```json ... ``` or ``` ... ``` patterns
    json_patterns = [
        r"```json\s*\n(.*?)\n```",  # ```json ... ```
        r"```\s*\n(.*?)\n```",  # ``` ... ```
        r"`(.*?)`",  # Single backticks
    ]

    for pattern in json_patterns:
        matches = re.findall(pattern, response_content, re.DOTALL)
        for match in matches:
            try:
                # Clean up the extracted content
                cleaned_content = match.strip()
                # Fix common JSON formatting issues
                cleaned_content = fix_json_formatting(cleaned_content)
                data = json.loads(cleaned_content)
                return normalize_json_structure(data)
            except json.JSONDecodeError:
                continue

    # If all else fails, try to find JSON-like content using regex
    # Look for content that starts with { and ends with }
    json_like_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
    matches = re.findall(json_like_pattern, response_content, re.DOTALL)

    for match in matches:
        try:
            data = json.loads(match)
            return normalize_json_structure(data)
        except json.JSONDecodeError:
            continue

    logger.error(f"Failed to parse JSON from response: {response_content[:200]}...")
    return None


def normalize_json_structure(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Normalize JSON structure to match our Pydantic models.

    This function handles field name mismatches between AI model responses
    and our expected data structures.

    Args:
        data: The parsed JSON data

    Returns:
        Normalized JSON data
    """
    if not isinstance(data, dict):
        return data

    # Handle candidates array normalization
    if "candidates" in data and isinstance(data["candidates"], list):
        normalized_candidates = []
        for candidate in data["candidates"]:
            if isinstance(candidate, dict):
                normalized_candidate = candidate.copy()

                # Convert main_category to category
                if (
                    "main_category" in normalized_candidate
                    and "category" not in normalized_candidate
                ):
                    normalized_candidate["category"] = normalized_candidate.pop(
                        "main_category"
                    )

                # Convert sub_category to category
                if (
                    "sub_category" in normalized_candidate
                    and "category" not in normalized_candidate
                ):
                    normalized_candidate["category"] = normalized_candidate.pop(
                        "sub_category"
                    )

                normalized_candidates.append(normalized_candidate)

        data["candidates"] = normalized_candidates

    return data


def fix_json_formatting(json_str: str) -> str:
    """
    Fix common JSON formatting issues in AI model responses.

    Args:
        json_str: The JSON string to fix

    Returns:
        Fixed JSON string
    """
    # Fix confidence values like "0. ninety" -> "0.90"
    json_str = re.sub(r'"confidence":\s*0\.\s*ninety', '"confidence": 0.90', json_str)
    json_str = re.sub(r'"confidence":\s*0\.\s*eighty', '"confidence": 0.80', json_str)
    json_str = re.sub(r'"confidence":\s*0\.\s*seventy', '"confidence": 0.70', json_str)
    json_str = re.sub(r'"confidence":\s*0\.\s*sixty', '"confidence": 0.60', json_str)
    json_str = re.sub(r'"confidence":\s*0\.\s*fifty', '"confidence": 0.50', json_str)

    # Fix other common number formatting issues
    json_str = re.sub(
        r'"confidence":\s*(\d+)\.\s*(\w+)', r'"confidence": \1.0', json_str
    )

    # Fix trailing commas
    json_str = re.sub(r",\s*}", "}", json_str)
    json_str = re.sub(r",\s*]", "]", json_str)

    return json_str


def extract_json_from_markdown(content: str) -> Optional[str]:
    """
    Extract JSON content from markdown code blocks.

    Args:
        content: The markdown content containing JSON

    Returns:
        Extracted JSON string, or None if not found
    """
    # Try different markdown patterns
    patterns = [
        r"```json\s*\n(.*?)\n```",
        r"```\s*\n(.*?)\n```",
        r"`([^`]+)`",
    ]

    for pattern in patterns:
        match = re.search(pattern, content, re.DOTALL)
        if match:
            return match.group(1).strip()

    return None


def validate_json_structure(data: Dict[str, Any], required_fields: list[str]) -> bool:
    """
    Validate that a JSON object contains all required fields.

    Args:
        data: The JSON data to validate
        required_fields: List of required field names

    Returns:
        True if all required fields are present, False otherwise
    """
    if not isinstance(data, dict):
        return False

    for field in required_fields:
        if field not in data:
            logger.warning(f"Missing required field: {field}")
            return False

    return True


def safe_get_nested_value(
    data: Dict[str, Any], keys: list[str], default: Any = None
) -> Any:
    """
    Safely get a nested value from a dictionary.

    Args:
        data: The dictionary to search
        keys: List of keys representing the path to the value
        default: Default value to return if path doesn't exist

    Returns:
        The value at the specified path, or default if not found
    """
    current = data
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    return current
