#!/usr/bin/env python3
"""
Test script to verify enum constraints are working correctly.
"""

import os
import sys

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from complaint_classifier.category_mapping import (
    get_main_category_for_sub,
    get_sub_categories_for_main,
)
from complaint_classifier.models import (
    ActionType,
    ClassificationResult,
    MainCategory,
    MainCategoryCandidate,
    SubCategory,
    SubCategoryCandidate,
    SubCategoryResult,
    TriageResult,
)


def test_enum_creation():
    """Test that enums can be created and used correctly."""
    print("Testing enum creation...")

    # Test MainCategory enum
    main_cat = MainCategory.HEALTH_ADMINISTRATION
    print(f"Main category: {main_cat} (value: {main_cat.value})")

    # Test SubCategory enum
    sub_cat = SubCategory.FOOD_SAFETY_HYGIENE
    print(f"Sub category: {sub_cat} (value: {sub_cat.value})")

    print("✓ Enum creation test passed")


def test_model_creation():
    """Test that models can be created with enum constraints."""
    print("\nTesting model creation...")

    # Test MainCategoryCandidate
    main_candidate = MainCategoryCandidate(
        category=MainCategory.HEALTH_ADMINISTRATION,
        confidence=0.85,
        reasoning="Test reasoning",
        keywords_found=["test", "keyword"],
    )
    print(f"Main candidate: {main_candidate.category}")

    # Test SubCategoryCandidate
    sub_candidate = SubCategoryCandidate(
        category=SubCategory.FOOD_SAFETY_HYGIENE,
        confidence=0.90,
        reasoning="Test sub reasoning",
        evidence_from_text="Test evidence",
    )
    print(f"Sub candidate: {sub_candidate.category}")

    # Test TriageResult
    triage_result = TriageResult(
        action=ActionType.FINALIZE,
        category=MainCategory.HEALTH_ADMINISTRATION,
        confidence=0.80,
        reasoning="Test triage reasoning",
    )
    print(f"Triage result: {triage_result.action}, {triage_result.category}")

    # Test ClassificationResult
    classification_result = ClassificationResult(
        category=SubCategory.FOOD_SAFETY_HYGIENE,
        confidence=0.95,
        path_taken="fast_track",
        reasoning_chain=[{"step": "step1"}, {"step": "step2"}],
        review_passed=True,
    )
    print(f"Classification result: {classification_result.category}")

    # Test SubCategoryResult
    sub_result = SubCategoryResult(
        parent_category=MainCategory.HEALTH_ADMINISTRATION, candidates=[sub_candidate]
    )
    print(f"Sub result parent: {sub_result.parent_category}")

    print("✓ Model creation test passed")


def test_category_mapping():
    """Test category mapping utilities."""
    print("\nTesting category mapping...")

    # Test getting sub-categories for main category
    main_cat = MainCategory.HEALTH_ADMINISTRATION
    sub_cats = get_sub_categories_for_main(main_cat)
    print(f"Sub-categories for {main_cat}: {len(sub_cats)} found")

    # Test getting main category for sub-category
    sub_cat = SubCategory.FOOD_SAFETY_HYGIENE
    parent_cat = get_main_category_for_sub(sub_cat)
    print(f"Parent of {sub_cat}: {parent_cat}")

    # Test mapping consistency
    assert parent_cat == main_cat, "Mapping inconsistency detected!"

    print("✓ Category mapping test passed")


def test_enum_validation():
    """Test that invalid values are rejected."""
    print("\nTesting enum validation...")

    try:
        # This should work
        valid_candidate = MainCategoryCandidate(
            category=MainCategory.HEALTH_ADMINISTRATION,
            confidence=0.85,
            reasoning="Valid test",
            keywords_found=[],
        )
        print("✓ Valid enum value accepted")

        # Test that string values are converted properly
        # (Pydantic should handle this automatically)

    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

    print("✓ Enum validation test passed")


def main():
    """Run all tests."""
    print("Running enum constraint tests...\n")

    try:
        test_enum_creation()
        test_model_creation()
        test_category_mapping()
        test_enum_validation()

        print("\n🎉 All tests passed! Enum constraints are working correctly.")
        return True

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
