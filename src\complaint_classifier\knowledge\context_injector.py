"""
Context injector for dynamically creating agent prompts with relevant knowledge.
"""

import logging
from typing import Dict, List, Optional

from .manager import KnowledgeManager
from .models import ContextData, SubCategoryInfo
from ..models import MainCategory, SubCategory

logger = logging.getLogger(__name__)


class ContextInjector:
    """Handles intelligent context injection for different agents."""
    
    def __init__(self, knowledge_manager: KnowledgeManager):
        """Initialize the context injector.
        
        Args:
            knowledge_manager: Knowledge manager instance
        """
        self.knowledge_manager = knowledge_manager
    
    def create_triage_context(self) -> ContextData:
        """Create context for Triage Agent.
        
        Returns:
            Context data with all categories for fast classification
        """
        # Get all categories from knowledge base
        all_sub_categories = {}
        main_categories = self.knowledge_manager.get_main_category_definitions()
        
        # Convert string keys to enum types
        typed_main_categories = {}
        for main_cat_str, main_desc in main_categories.items():
            try:
                main_cat_enum = MainCategory(main_cat_str)
                typed_main_categories[main_cat_enum] = main_desc
            except ValueError:
                logger.warning(f"Unknown main category: {main_cat_str}")
                continue
                
            sub_cats = self.knowledge_manager.get_sub_category_definitions(main_cat_str)
            for sub_name_str, sub_desc in sub_cats.items():
                try:
                    sub_cat_enum = SubCategory(sub_name_str)
                    all_sub_categories[sub_cat_enum] = SubCategoryInfo(
                        description=sub_desc,
                        parent_category=main_cat_enum
                    )
                except ValueError:
                    logger.warning(f"Unknown sub category: {sub_name_str}")
                    continue
        
        logger.debug(f"Created triage context with {len(all_sub_categories)} sub-categories")
        
        return ContextData(
            main_categories=typed_main_categories,
            sub_categories=all_sub_categories
        )
    
    def create_main_category_context(self) -> ContextData:
        """Create context for Main Category Analyzer Agent.
        
        Returns:
            Context data with main category definitions
        """
        main_categories = self.knowledge_manager.get_main_category_definitions()
        
        # Convert string keys to enum types
        typed_main_categories = {}
        for main_cat_str, main_desc in main_categories.items():
            try:
                main_cat_enum = MainCategory(main_cat_str)
                typed_main_categories[main_cat_enum] = main_desc
            except ValueError:
                logger.warning(f"Unknown main category: {main_cat_str}")
                continue
        
        logger.debug(f"Created main category context with {len(typed_main_categories)} categories")
        
        return ContextData(main_categories=typed_main_categories)
    
    def create_sub_category_context(self, main_category: str) -> ContextData:
        """Create context for Sub Category Analyzer Agent.
        
        Args:
            main_category: The main category to get sub-categories for
            
        Returns:
            Context data with sub-category definitions for the specified main category
        """
        sub_categories = self.knowledge_manager.get_sub_category_definitions(main_category)
        
        # Convert string keys to enum types
        typed_sub_categories = {}
        for sub_name_str, sub_desc in sub_categories.items():
            try:
                sub_cat_enum = SubCategory(sub_name_str)
                typed_sub_categories[sub_cat_enum] = sub_desc
            except ValueError:
                logger.warning(f"Unknown sub category: {sub_name_str}")
                continue
        
        # Convert main category string to enum
        try:
            main_cat_enum = MainCategory(main_category)
        except ValueError:
            logger.warning(f"Unknown main category: {main_category}")
            main_cat_enum = None
        
        logger.debug(f"Created sub-category context for '{main_category}' with {len(typed_sub_categories)} sub-categories")
        
        return ContextData(
            sub_categories=typed_sub_categories,
            selected_main_category=main_cat_enum
        )
    
    def create_review_context(self, main_category: str, sub_category: str) -> ContextData:
        """Create context for Review Agent.
        
        Args:
            main_category: The selected main category
            sub_category: The selected sub-category
            
        Returns:
            Context data with both main and sub-category definitions
        """
        # Get the specific definitions for validation
        main_cat_desc = self.knowledge_manager.knowledge_base.get_main_category_description(main_category)
        sub_cat_desc = self.knowledge_manager.knowledge_base.get_sub_category_description(main_category, sub_category)
        
        # Convert to enum types
        typed_main_categories = {}
        typed_sub_categories = {}
        selected_main_enum = None
        selected_sub_enum = None
        
        if main_cat_desc:
            try:
                main_cat_enum = MainCategory(main_category)
                typed_main_categories[main_cat_enum] = main_cat_desc
                selected_main_enum = main_cat_enum
            except ValueError:
                logger.warning(f"Unknown main category: {main_category}")
        
        if sub_cat_desc:
            try:
                sub_cat_enum = SubCategory(sub_category)
                typed_sub_categories[sub_cat_enum] = sub_cat_desc
                selected_sub_enum = sub_cat_enum
            except ValueError:
                logger.warning(f"Unknown sub category: {sub_category}")
        
        logger.debug(f"Created review context for '{main_category}' -> '{sub_category}'")
        
        return ContextData(
            main_categories=typed_main_categories,
            sub_categories=typed_sub_categories,
            selected_main_category=selected_main_enum,
            selected_sub_category=selected_sub_enum
        )
    
    def format_main_category_prompt_section(self, context: ContextData) -> str:
        """Format main category definitions for prompt injection.
        
        Args:
            context: Context data with main categories
            
        Returns:
            Formatted string for prompt injection
        """
        if not context.main_categories:
            return ""
        
        sections = []
        sections.append("**主案類清單與定義:**")
        
        for main_cat_enum, description in context.main_categories.items():
            sections.append(f"- **{main_cat_enum.value}**: {description}")
        
        return "\n".join(sections)
    
    def format_sub_category_prompt_section(self, context: ContextData) -> str:
        """Format sub-category definitions for prompt injection.
        
        Args:
            context: Context data with sub-categories
            
        Returns:
            Formatted string for prompt injection
        """
        if not context.sub_categories or not context.selected_main_category:
            return ""
        
        sections = []
        sections.append(f"**【{context.selected_main_category.value}】子案類清單與定義:**")
        
        for sub_cat_enum, description in context.sub_categories.items():
            # Handle both string and SubCategoryInfo types
            if isinstance(description, SubCategoryInfo):
                desc_text = description.description
            else:
                desc_text = description
            sections.append(f"- {sub_cat_enum.value}: {desc_text}")
        
        return "\n".join(sections)
    
    def format_triage_prompt_section(self, context: ContextData) -> str:
        """Format all sub-categories for triage agent prompt injection.
        
        Args:
            context: Context data with all sub-categories
            
        Returns:
            Formatted string for prompt injection
        """
        if not context.sub_categories:
            return ""
        
        sections = []
        sections.append("**可用的子分類清單：**")
        
        # Group by main category for better organization
        grouped = {}
        for sub_cat_enum, sub_info in context.sub_categories.items():
            if isinstance(sub_info, SubCategoryInfo) and sub_info.parent_category:
                parent = sub_info.parent_category
                if parent not in grouped:
                    grouped[parent] = []
                grouped[parent].append((sub_cat_enum, sub_info.description))
        
        # Format grouped categories
        for main_cat_enum, sub_cats in grouped.items():
            sections.append(f"\n【{main_cat_enum.value}】")
            for sub_cat_enum, sub_desc in sub_cats:
                sections.append(f"- {sub_cat_enum.value}: {sub_desc}")
        
        return "\n".join(sections)
    
    def format_review_prompt_section(self, context: ContextData) -> str:
        """Format definitions for review agent prompt injection.
        
        Args:
            context: Context data with selected categories
            
        Returns:
            Formatted string for prompt injection
        """
        sections = []
        
        if context.main_categories and context.selected_main_category:
            main_desc = context.main_categories.get(context.selected_main_category, "")
            sections.append("**官方定義查核:**")
            sections.append(f"- **主分類定義 ('{context.selected_main_category.value}'):** '{main_desc}'")
        
        if context.sub_categories and context.selected_sub_category:
            sub_info = context.sub_categories.get(context.selected_sub_category, "")
            if isinstance(sub_info, SubCategoryInfo):
                sub_desc = sub_info.description
            else:
                sub_desc = sub_info
            sections.append(f"- **子分類定義 ('{context.selected_sub_category.value}'):** '{sub_desc}'")
        
        return "\n".join(sections)
