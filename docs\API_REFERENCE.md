# API 參考文檔

本文檔詳細說明 AI Multi-Agent Complaint Classification System 的 API 介面。

## 📋 目錄

-   [ComplaintClassifier](#complaintclassifier)
-   [數據模型](#數據模型)
-   [配置類別](#配置類別)
-   [知識庫管理](#知識庫管理)
-   [工作流引擎](#工作流引擎)
-   [錯誤處理](#錯誤處理)

## ComplaintClassifier

主要的分類器介面，提供陳情案件分類功能。

### 初始化

```python
from complaint_classifier import ComplaintClassifier
from complaint_classifier.config import Settings

# 使用預設設置
classifier = ComplaintClassifier()

# 使用自定義設置
settings = Settings()
settings.workflow.enable_fast_track = False
classifier = ComplaintClassifier(settings)
```

### 方法

#### `classify(content, case_id=None, metadata=None)`

分類陳情案件的主要方法。

**參數:**

-   `content` (str): 陳情內容文本
-   `case_id` (str, optional): 案件編號
-   `metadata` (dict, optional): 額外的元數據

**返回:**

-   `ClassificationResult`: 分類結果對象

**示例:**

```python
result = await classifier.classify(
    content="路燈不亮了，請派人修理",
    case_id="CASE001",
    metadata={"source": "web", "priority": "normal"}
)

print(f"分類: {result.category}")
print(f"信心度: {result.confidence}")
print(f"路徑: {result.path_taken}")
```

#### `get_available_categories()`

獲取所有可用的分類類別。

**返回:**

-   `Dict[str, Dict[str, str]]`: 主分類及其子分類的字典

**示例:**

```python
categories = classifier.get_available_categories()
for main_cat, data in categories.items():
    print(f"主分類: {main_cat}")
    print(f"描述: {data['description']}")
    print(f"子分類: {list(data['sub_categories'].keys())}")
```

#### `get_statistics()`

獲取系統統計信息。

**返回:**

-   `Dict[str, Any]`: 包含知識庫和配置統計的字典

**示例:**

```python
stats = classifier.get_statistics()
print(f"主分類數量: {stats['knowledge_base']['main_categories']}")
print(f"子分類數量: {stats['knowledge_base']['total_sub_categories']}")
print(f"快速通道: {stats['configuration']['fast_track_enabled']}")
```

#### `validate_setup()`

驗證系統設置是否正確。

**返回:**

-   `Dict[str, bool]`: 各組件的驗證結果

**示例:**

```python
validation = await classifier.validate_setup()
for component, status in validation.items():
    print(f"{component}: {'✓' if status else '✗'}")
```

## 數據模型

### ComplaintCase

陳情案件的數據模型。

```python
from complaint_classifier.models import ComplaintCase

case = ComplaintCase(
    content="陳情內容",
    case_id="CASE001",
    metadata={"source": "web"}
)
```

**屬性:**

-   `content` (str): 陳情內容
-   `case_id` (str, optional): 案件編號
-   `metadata` (dict, optional): 元數據

### ClassificationResult

分類結果的數據模型。

```python
from complaint_classifier.models import ClassificationResult

result = ClassificationResult(
    category="路燈維護",
    confidence=0.95,
    path_taken="fast_track",
    reasoning_chain=[...],
    review_passed=True
)
```

**屬性:**

-   `category` (str): 分類結果
-   `confidence` (float, optional): 信心度 (0-1)
-   `path_taken` (Literal["fast_track", "expert_review"]): 處理路徑
-   `reasoning_chain` (List[Dict]): 推理鏈
-   `review_passed` (bool, optional): 審查結果

### TriageResult

分流結果的數據模型。

```python
from complaint_classifier.models import TriageResult, ActionType

result = TriageResult(
    action=ActionType.FINALIZE,
    category="路燈維護",
    confidence=0.9,
    reasoning="明確的路燈故障問題"
)
```

**屬性:**

-   `action` (ActionType): 動作類型 (FINALIZE 或 ESCALATE)
-   `category` (str): 建議分類
-   `confidence` (float): 信心度
-   `reasoning` (str): 推理說明

### CategoryCandidate

分類候選項的數據模型。

```python
from complaint_classifier.models import CategoryCandidate

candidate = CategoryCandidate(
    category="路燈維護",
    confidence=0.95,
    reasoning="內容明確提到路燈故障",
    keywords_found=["路燈", "不亮", "故障"],
    evidence_from_text="路燈不亮了"
)
```

**屬性:**

-   `category` (str): 分類名稱
-   `confidence` (float): 信心度
-   `reasoning` (str): 推理說明
-   `keywords_found` (List[str], optional): 找到的關鍵詞
-   `evidence_from_text` (str, optional): 文本證據

## 配置類別

### Settings

主要的應用程式設置類別。

```python
from complaint_classifier.config import Settings

settings = Settings()

# 環境設置
settings.environment = "production"
settings.debug = False
settings.log_level = "INFO"

# 知識庫設置
settings.knowledge_base_path = "data/categories_def.json"

# API 金鑰
settings.openai_api_key = "your-api-key"
settings.anthropic_api_key = "your-api-key"
```

### ModelConfig

AI 模型配置類別。

```python
from complaint_classifier.config import ModelConfig

model_config = ModelConfig(
    provider="openai",
    model_id="gpt-4.1-mini",
    temperature=0.1,
    max_tokens=1000
)
```

### AgentConfig

代理配置類別。

```python
from complaint_classifier.config import AgentConfig

agent_config = AgentConfig()

# 自定義分流代理模型
agent_config.triage_model.model_id = "gpt-4.1-mini"
agent_config.triage_model.temperature = 0.05

# 自定義主分析器模型
agent_config.main_analyzer_model.model_id = "gpt-4.1-mini"
agent_config.main_analyzer_model.temperature = 0.1
```

### WorkflowConfig

工作流配置類別。

```python
from complaint_classifier.config import WorkflowConfig

workflow_config = WorkflowConfig(
    triage_confidence_threshold=0.85,
    enable_fast_track=True,
    fallback_category="其他建議、諮詢或陳情"
)
```

## 知識庫管理

### KnowledgeManager

知識庫管理器，負責載入和管理分類定義。

```python
from complaint_classifier.knowledge import KnowledgeManager

# 初始化
km = KnowledgeManager("data/categories_def.json")

# 載入知識庫
kb = km.load_knowledge_base()

# 獲取主分類定義
main_categories = km.get_main_category_definitions()

# 獲取子分類定義
sub_categories = km.get_sub_category_definitions("道路、水溝維護")

# 驗證分類
is_valid = km.validate_classification("道路、水溝維護", "路面破損")

# 獲取統計信息
stats = km.get_statistics()
```

### ContextInjector

上下文注入器，用於動態生成代理提示。

```python
from complaint_classifier.knowledge import ContextInjector

injector = ContextInjector(knowledge_manager)

# 創建主分類上下文
main_context = injector.create_main_category_context()

# 創建子分類上下文
sub_context = injector.create_sub_category_context("道路、水溝維護")

# 創建審查上下文
review_context = injector.create_review_context("道路、水溝維護", "路面破損")

# 格式化提示段落
prompt_section = injector.format_main_category_prompt_section(main_context)
```

## 工作流引擎

### WorkflowEngine

工作流引擎，協調整個分類流程。

```python
from complaint_classifier.workflow import WorkflowEngine
from complaint_classifier.config import Settings

# 初始化
settings = Settings()
engine = WorkflowEngine(settings)

# 分類陳情
result = await engine.classify_complaint(complaint_case)
```

## 錯誤處理

### 異常類型

系統定義了以下異常類型：

```python
# 知識庫相關錯誤
class KnowledgeBaseError(Exception):
    """知識庫載入或解析錯誤"""
    pass

# 代理執行錯誤
class AgentExecutionError(Exception):
    """代理執行過程中的錯誤"""
    pass

# 配置錯誤
class ConfigurationError(Exception):
    """配置相關錯誤"""
    pass
```

### 錯誤處理示例

```python
try:
    result = await classifier.classify("陳情內容")
except KnowledgeBaseError as e:
    print(f"知識庫錯誤: {e}")
except AgentExecutionError as e:
    print(f"代理執行錯誤: {e}")
except ConfigurationError as e:
    print(f"配置錯誤: {e}")
except Exception as e:
    print(f"未知錯誤: {e}")
```

## 最佳實踐

### 1. 異步使用

```python
import asyncio

async def classify_multiple(complaints):
    classifier = ComplaintClassifier()
    tasks = [
        classifier.classify(content, case_id=f"CASE_{i}")
        for i, content in enumerate(complaints)
    ]
    return await asyncio.gather(*tasks)

# 使用
complaints = ["路燈故障", "道路破損", "垃圾清運"]
results = asyncio.run(classify_multiple(complaints))
```

### 2. 批量處理

```python
async def batch_classify(complaints, batch_size=10):
    classifier = ComplaintClassifier()
    results = []

    for i in range(0, len(complaints), batch_size):
        batch = complaints[i:i+batch_size]
        batch_tasks = [
            classifier.classify(content)
            for content in batch
        ]
        batch_results = await asyncio.gather(*batch_tasks)
        results.extend(batch_results)

        # 避免 API 限制
        await asyncio.sleep(1)

    return results
```

### 3. 錯誤重試

```python
import asyncio
from tenacity import retry, stop_after_attempt, wait_exponential

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
async def robust_classify(classifier, content):
    return await classifier.classify(content)

# 使用
try:
    result = await robust_classify(classifier, "陳情內容")
except Exception as e:
    print(f"重試後仍然失敗: {e}")
```
