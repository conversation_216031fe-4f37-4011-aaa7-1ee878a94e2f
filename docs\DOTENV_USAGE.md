# Python-dotenv 使用指南

本專案已經整合了 `python-dotenv` 套件來管理環境變數。以下是使用方法和範例。

## 📦 套件安裝

`python-dotenv` 已經包含在專案依賴中：

```toml
# pyproject.toml
dependencies = [
    "python-dotenv>=1.0.0",
    # ... 其他依賴
]
```

## 🔧 基本設定

### 1. 創建 .env 檔案

專案根目錄已經有 `.env` 檔案，您可以直接編輯：

```bash
# AI Model API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Knowledge Base
KNOWLEDGE_BASE_PATH=src/data/categories_def.json
```

### 2. 載入環境變數

在 Python 程式中載入 .env 檔案：

```python
from dotenv import load_dotenv
import os

# 載入 .env 檔案
load_dotenv()

# 讀取環境變數
api_key = os.getenv("OPENAI_API_KEY")
environment = os.getenv("ENVIRONMENT", "development")  # 帶預設值
```

## 🏗️ 專案整合

### Settings 類別自動載入

本專案的 `Settings` 類別已經配置為自動載入 .env 檔案：

```python
# src/complaint_classifier/config.py
class Settings(BaseSettings):
    # ...
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        env_nested_delimiter = "__"  # 支援嵌套配置
```

### 嵌套配置支援

使用 `__` 分隔符來設定嵌套配置：

```bash
# .env 檔案
WORKFLOW__TRIAGE_CONFIDENCE_THRESHOLD=0.85
WORKFLOW__ENABLE_FAST_TRACK=true

AGENTS__TRIAGE_MODEL__PROVIDER=openai
AGENTS__TRIAGE_MODEL__MODEL_ID=gpt-4.1-mini
```

這會對應到：

```python
settings.workflow.triage_confidence_threshold = 0.85
settings.workflow.enable_fast_track = True
settings.agents.triage_model.provider = "openai"
settings.agents.triage_model.model_id = "gpt-4.1-mini"
```

## 📋 使用範例

### 1. 基本使用

```python
from dotenv import load_dotenv
import os

# 載入環境變數
load_dotenv()

# 讀取 API 金鑰
openai_key = os.getenv("OPENAI_API_KEY")
if openai_key and openai_key != "your_openai_api_key_here":
    print("✅ OpenAI API 金鑰已設定")
else:
    print("❌ 請設定 OpenAI API 金鑰")
```

### 2. 使用 Settings 類別

```python
from complaint_classifier.config import Settings

# Settings 會自動載入 .env 檔案
settings = Settings()

print(f"環境: {settings.environment}")
print(f"除錯模式: {settings.debug}")
print(f"API 金鑰: {'已設定' if settings.openai_api_key else '未設定'}")
```

### 3. 讀取所有變數

```python
from dotenv import dotenv_values

# 讀取所有環境變數到字典
config = dotenv_values(".env")

for key, value in config.items():
    print(f"{key}: {value}")
```

## 🔍 驗證和除錯

### 執行範例程式

```bash
# 測試 dotenv 功能
python examples/dotenv_example.py

# 測試完整系統
python examples/basic_usage.py
```

### 檢查環境變數載入

```python
import os
from pathlib import Path

# 檢查 .env 檔案是否存在
env_file = Path(".env")
if env_file.exists():
    print(f"✅ .env 檔案存在: {env_file.absolute()}")
else:
    print("❌ .env 檔案不存在")

# 檢查特定環境變數
required_vars = ["OPENAI_API_KEY", "ANTHROPIC_API_KEY"]
for var in required_vars:
    value = os.getenv(var)
    if value and value != f"your_{var.lower()}_here":
        print(f"✅ {var}: 已設定")
    else:
        print(f"❌ {var}: 未設定")
```

## 🔒 安全注意事項

1. **不要提交 .env 檔案到版本控制**

    ```bash
    # .gitignore
    .env
    ```

2. **使用 .env.example 作為範本**

    ```bash
    cp .env.example .env
    # 然後編輯 .env 設定實際的 API 金鑰
    ```

3. **在生產環境使用真實的環境變數**
    ```bash
    export OPENAI_API_KEY="real-api-key"
    export ENVIRONMENT="production"
    ```

## 🚀 進階功能

### 1. 覆蓋現有環境變數

```python
load_dotenv(override=True)  # 覆蓋已存在的環境變數
```

### 2. 詳細載入資訊

```python
load_dotenv(verbose=True)  # 顯示載入過程
```

### 3. 自動尋找 .env 檔案

```python
from dotenv import find_dotenv
load_dotenv(find_dotenv())  # 自動尋找最近的 .env 檔案
```

### 4. 指定 .env 檔案路徑

```python
load_dotenv("/path/to/your/.env")
```

## 📚 相關資源

-   [python-dotenv 官方文檔](https://python-dotenv.readthedocs.io/)
-   [Pydantic Settings 文檔](https://docs.pydantic.dev/latest/concepts/pydantic_settings/)
-   專案範例：`examples/dotenv_example.py`
-   專案範例：`examples/basic_usage.py`
