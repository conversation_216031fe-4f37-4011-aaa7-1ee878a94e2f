"""
Agno Workflow implementation for complaint classification.

This module implements the complaint classification workflow using <PERSON>gno's
official Workflow class, replacing the custom WorkflowEngine with a
declarative, event-driven approach.
"""

import logging
from typing import Any, Dict, Optional

from agno.workflow.v2 import Step, Workflow, StepInput, StepOutput
from agno.storage.sqlite import SqliteStorage

from ..agents import (
    MainCategoryAnalyzer,
    ReviewAgent,
    SubCategoryAnalyzer,
    OutputAgent,
)
from ..config import Settings
from ..knowledge.context_injector import ContextInjector
from ..knowledge.manager import KnowledgeManager
from ..models import (
    ClassificationResult,
    ComplaintCase,
    MainCategory,
    MainCategoryResult,
    ReviewResult,
    SubCategoryResult,
    DecisionType,
    SubCategory,
)
from ..category_mapping import get_main_category_for_sub
from ..usage.tracker import get_usage_tracker

logger = logging.getLogger(__name__)


class ComplaintClassificationWorkflow(Workflow):
    """
    Agno-based complaint classification workflow.
    
    This workflow replaces the custom WorkflowEngine with <PERSON>gno's declarative
    workflow management, providing better error handling, state management,
    and extensibility.
    """

    def __init__(self, settings: Settings):
        """Initialize the Agno workflow.
        
        Args:
            settings: Application settings
        """
        self.settings = settings
        
        # Initialize knowledge management
        self.knowledge_manager = KnowledgeManager(settings.knowledge_base_path)
        self.context_injector = ContextInjector(self.knowledge_manager)
        
        # Initialize agents
        self._init_agents()
        
        # Initialize workflow session state for data sharing between steps
        # Use a class-level dictionary to ensure persistence across step executions
        ComplaintClassificationWorkflow._session_state = {}
        
        # Create workflow steps
        steps = self._create_workflow_steps()
        
        # Initialize parent Workflow
        super().__init__(
            name="AI Complaint Classification Expert System",
            description="Multi-agent expert committee for intelligent complaint classification using specialized AI agents",
            steps=steps,
            storage=self._create_storage(),
        )
        
        logger.info("Agno Complaint Classification Workflow initialized")

    def _init_agents(self):
        """Initialize all agents with their configurations."""
        self.main_analyzer = MainCategoryAnalyzer(
            self.settings.agents.main_analyzer_model, 
            self.context_injector
        )
        self.sub_analyzer = SubCategoryAnalyzer(
            self.settings.agents.sub_analyzer_model, 
            self.context_injector
        )
        self.review_agent = ReviewAgent(
            self.settings.agents.review_model, 
            self.context_injector
        )
        self.output_agent = OutputAgent(
            self.settings.agents.orchestrator_model
        )
        
        logger.debug("All agents initialized for Agno workflow")

    def _create_storage(self) -> SqliteStorage:
        """Create storage backend for the workflow."""
        return SqliteStorage(
            table_name="agno_workflow_sessions",
            db_file="tmp/agno_workflows.db",
            mode="workflow_v2",
        )

    def _create_workflow_steps(self) -> list[Step]:
        """Create the workflow steps from agents.
        
        Returns:
            List of workflow steps
        """
        return [
            Step(
                name="main_category_analysis",
                description="Analyze complaint for main category classification",
                executor=self._main_category_step,
            ),
            Step(
                name="sub_category_analysis", 
                description="Perform fine-grained sub-category analysis",
                executor=self._sub_category_step,
            ),
            Step(
                name="review_analysis",
                description="Quality assurance and validation review",
                executor=self._review_step,
            ),
            Step(
                name="output_formatting",
                description="Format final classification result",
                executor=self._output_step,
            ),
        ]

    async def _main_category_step(self, step_input: StepInput) -> StepOutput:
        """Execute main category analysis step.
        
        Args:
            step_input: Input from previous step or workflow start
            
        Returns:
            Step output with main category results
        """
        try:
            # Extract complaint from step input
            complaint = self._extract_complaint_from_input(step_input)
            
            logger.info(f"Starting main category analysis for case: {complaint.case_id}")
            
            # Execute main category analysis
            main_result = await self.main_analyzer.process(complaint)
            
            # Store in workflow session state for next step
            ComplaintClassificationWorkflow._session_state["main_result"] = main_result.model_dump()
            ComplaintClassificationWorkflow._session_state["complaint"] = complaint.model_dump()
            
            return StepOutput(
                content=main_result.model_dump(),
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error in main category analysis: {e}")
            return StepOutput(
                content=f"Main category analysis failed: {str(e)}",
                success=False,
                error=str(e)
            )

    async def _sub_category_step(self, step_input: StepInput) -> StepOutput:
        """Execute sub-category analysis step.
        
        Args:
            step_input: Input from main category step
            
        Returns:
            Step output with sub-category results
        """
        try:
            # Get data from workflow session state
            complaint_data = ComplaintClassificationWorkflow._session_state.get("complaint")
            main_result_data = ComplaintClassificationWorkflow._session_state.get("main_result")
            
            if not complaint_data or not main_result_data:
                raise ValueError("Missing required data from previous step")
            
            # Reconstruct objects
            complaint = ComplaintCase(**complaint_data)
            main_result = MainCategoryResult(**main_result_data)
            
            # Get main category from results
            main_category = (
                main_result.candidates[0].category 
                if main_result.candidates 
                else None
            )
            
            if not main_category:
                raise ValueError("No main category candidates found")
            
            logger.info(f"Starting sub-category analysis for main category: {main_category.value}")
            
            # Execute sub-category analysis
            sub_result = await self.sub_analyzer.process(complaint, main_category=main_category)
            
            # Store in workflow session state
            ComplaintClassificationWorkflow._session_state["sub_result"] = sub_result.model_dump()
            
            return StepOutput(
                content=sub_result.model_dump(),
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error in sub-category analysis: {e}")
            return StepOutput(
                content=f"Sub-category analysis failed: {str(e)}",
                success=False,
                error=str(e)
            )

    async def _review_step(self, step_input: StepInput) -> StepOutput:
        """Execute review analysis step with retry logic.
        
        Args:
            step_input: Input from sub-category step
            
        Returns:
            Step output with review results
        """
        max_retries = 2
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                # Get data from workflow session state
                complaint_data = ComplaintClassificationWorkflow._session_state.get("complaint")
                main_result_data = ComplaintClassificationWorkflow._session_state.get("main_result")
                sub_result_data = ComplaintClassificationWorkflow._session_state.get("sub_result")
                
                if not all([complaint_data, main_result_data, sub_result_data]):
                    raise ValueError("Missing required data from previous steps")
                
                # Reconstruct objects
                complaint = ComplaintCase(**complaint_data)
                main_result = MainCategoryResult(**main_result_data)
                sub_result = SubCategoryResult(**sub_result_data)
                
                logger.info(f"Starting review analysis (attempt {retry_count + 1}/{max_retries + 1})")
                
                # Execute review
                review_result = await self.review_agent.process(
                    complaint, 
                    main_result=main_result, 
                    sub_result=sub_result
                )
                
                # Store in workflow session state
                ComplaintClassificationWorkflow._session_state["review_result"] = review_result.model_dump()
                ComplaintClassificationWorkflow._session_state["retry_count"] = retry_count
                
                # Check if review passed or we've exhausted retries
                if review_result.decision == DecisionType.PASS or retry_count >= max_retries:
                    return StepOutput(
                        content=review_result.model_dump(),
                        success=True
                    )
                else:
                    # Review failed, retry
                    retry_count += 1
                    logger.warning(f"Review failed, retrying ({retry_count}/{max_retries})")
                    continue
                    
            except Exception as e:
                retry_count += 1
                logger.error(f"Error in review analysis (attempt {retry_count}): {e}")
                if retry_count > max_retries:
                    return StepOutput(
                        content=f"Review analysis failed after {retry_count} attempts: {str(e)}",
                        success=False,
                        error=str(e)
                    )
        
        # Should not reach here, but just in case
        return StepOutput(
            content="Review analysis completed with maximum retries",
            success=True,
            data={"decision": DecisionType.PASS.value, "retry_count": max_retries}
        )

    async def _output_step(self, step_input: StepInput) -> StepOutput:
        """Execute output formatting step.
        
        Args:
            step_input: Input from review step
            
        Returns:
            Final classification result
        """
        try:
            # Get all data from workflow session state
            complaint_data = ComplaintClassificationWorkflow._session_state.get("complaint")
            main_result_data = ComplaintClassificationWorkflow._session_state.get("main_result") 
            sub_result_data = ComplaintClassificationWorkflow._session_state.get("sub_result")
            review_result_data = ComplaintClassificationWorkflow._session_state.get("review_result")
            retry_count = ComplaintClassificationWorkflow._session_state.get("retry_count", 0)
            
            if not all([complaint_data, main_result_data, sub_result_data]):
                raise ValueError("Missing required data from previous steps")
            
            # Reconstruct objects
            complaint = ComplaintCase(**complaint_data)
            main_result = MainCategoryResult(**main_result_data)
            sub_result = SubCategoryResult(**sub_result_data)
            review_result = ReviewResult(**review_result_data) if review_result_data else None
            
            # Determine final category
            sub_category = (
                sub_result.candidates[0].category 
                if sub_result.candidates 
                else self.settings.workflow.fallback_category
            )
            main_category = get_main_category_for_sub(sub_category)
            
            # Extract reasoning
            main_category_reasoning = (
                main_result.candidates[0].reasoning 
                if main_result.candidates 
                else "無法判斷主類別"
            )
            sub_category_reasoning = (
                sub_result.candidates[0].reasoning 
                if sub_result.candidates 
                else "無法判斷子類別"
            )
            
            # Generate classification summary
            classification_summary = f"經專家委員會分析，案件屬於【{main_category.value}】主類別下的【{sub_category.value}】子類別。"
            if main_result.candidates and main_result.candidates[0].keywords_found:
                classification_summary += f"關鍵詞：{', '.join(main_result.candidates[0].keywords_found)}。"
            if sub_result.candidates and sub_result.candidates[0].evidence_from_text:
                classification_summary += f"案件證據：{sub_result.candidates[0].evidence_from_text}。"
            
            # Prepare classification data
            classification_data = {
                "category": sub_category,
                "main_category": main_category,
                "confidence": sub_result.candidates[0].confidence if sub_result.candidates else 0.0,
                "path_taken": "expert_review",
                "reasoning_chain": self._build_reasoning_chain(),
                "review_passed": review_result.decision == DecisionType.PASS if review_result else False,
                "main_category_reasoning": main_category_reasoning,
                "sub_category_reasoning": sub_category_reasoning, 
                "classification_summary": classification_summary,
            }
            
            logger.info(f"Starting output formatting for final category: {sub_category.value}")
            
            # Execute output formatting
            result = await self.output_agent.process(classification_data)
            
            logger.info(f"Classification completed: {result.category}")
            
            return StepOutput(
                content=result.model_dump(),
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error in output formatting: {e}")
            # Return fallback result
            return await self._create_fallback_result(complaint, e)

    def _extract_complaint_from_input(self, step_input: StepInput) -> ComplaintCase:
        """Extract ComplaintCase from step input.
        
        Args:
            step_input: Workflow step input
            
        Returns:
            ComplaintCase object
        """
        # If this is the first step, the complaint should be in the workflow message/data
        if hasattr(step_input, 'workflow_data') and step_input.workflow_data:
            return ComplaintCase(**step_input.workflow_data)
        elif hasattr(step_input, 'data') and step_input.data:
            return ComplaintCase(**step_input.data)
        else:
            # Try to get from workflow session state
            complaint_data = ComplaintClassificationWorkflow._session_state.get("complaint")
            if complaint_data:
                return ComplaintCase(**complaint_data)
            else:
                raise ValueError("No complaint data found in step input or workflow session state")

    def _build_reasoning_chain(self) -> list[Dict[str, Any]]:
        """Build reasoning chain from workflow session state.
        
        Returns:
            List of reasoning steps
        """
        reasoning_chain = []
        
        # Add main category step
        if "main_result" in ComplaintClassificationWorkflow._session_state:
            reasoning_chain.append({
                "agent": "MainCategoryAnalyzer",
                "input": ComplaintClassificationWorkflow._session_state.get("complaint", {}),
                "output": ComplaintClassificationWorkflow._session_state["main_result"],
                "metadata": {"step": "main_category_analysis"}
            })
        
        # Add sub-category step
        if "sub_result" in ComplaintClassificationWorkflow._session_state:
            reasoning_chain.append({
                "agent": "SubCategoryAnalyzer", 
                "input": {
                    "complaint": ComplaintClassificationWorkflow._session_state.get("complaint", {}),
                    "main_category": ComplaintClassificationWorkflow._session_state.get("main_result", {})
                },
                "output": ComplaintClassificationWorkflow._session_state["sub_result"],
                "metadata": {"step": "sub_category_analysis"}
            })
        
        # Add review step
        if "review_result" in ComplaintClassificationWorkflow._session_state:
            reasoning_chain.append({
                "agent": "ReviewAgent",
                "input": {
                    "complaint": ComplaintClassificationWorkflow._session_state.get("complaint", {}),
                    "main_result": ComplaintClassificationWorkflow._session_state.get("main_result", {}),
                    "sub_result": ComplaintClassificationWorkflow._session_state.get("sub_result", {})
                },
                "output": ComplaintClassificationWorkflow._session_state["review_result"],
                "metadata": {
                    "step": "review",
                    "retry_count": ComplaintClassificationWorkflow._session_state.get("retry_count", 0)
                }
            })
        
        return reasoning_chain

    async def _create_fallback_result(self, complaint: ComplaintCase, error: Exception) -> StepOutput:
        """Create fallback classification result for errors.
        
        Args:
            complaint: The original complaint case
            error: The error that occurred
            
        Returns:
            Fallback step output
        """
        logger.error(f"Creating fallback result due to error: {error}")
        
        # Use fallback category
        fallback_category = SubCategory.OTHER_SUGGESTIONS_CONSULTATIONS_COMPLAINTS
        fallback_main_category = get_main_category_for_sub(fallback_category)
        
        classification_data = {
            "category": fallback_category,
            "main_category": fallback_main_category,
            "confidence": 0.0,
            "path_taken": "expert_review",
            "reasoning_chain": self._build_reasoning_chain(),
            "review_passed": False,
            "main_category_reasoning": f"分類過程發生錯誤，預設分類為【{fallback_main_category.value}】",
            "sub_category_reasoning": f"由於系統錯誤，使用預設分類：{str(error)}",
            "classification_summary": f"分類過程發生錯誤，已使用預設分類【{fallback_main_category.value}】下的【其他建議、諮詢或陳情】。",
        }
        
        try:
            result = await self.output_agent.process(classification_data)
            return StepOutput(
                content=result.model_dump(),
                success=True
            )
        except Exception as output_error:
            logger.error(f"Error even in fallback output formatting: {output_error}")
            # Return basic fallback without agent processing
            basic_result = ClassificationResult(
                category=fallback_category,
                main_category=fallback_main_category,
                confidence=0.0,
                path_taken="expert_review",
                reasoning_chain=[],
                review_passed=False,
                main_category_reasoning=f"系統錯誤，使用預設分類",
                sub_category_reasoning=f"錯誤：{str(error)}",
                classification_summary="系統發生錯誤，使用預設分類。"
            )
            
            return StepOutput(
                content=basic_result.model_dump(),
                success=True
            )

    async def classify_complaint(self, complaint: ComplaintCase) -> ClassificationResult:
        """Main entry point for complaint classification.
        
        Args:
            complaint: The complaint case to classify
            
        Returns:
            Classification result
        """
        try:
            logger.info(f"Starting Agno workflow classification for case: {complaint.case_id}")
            
            # Run the workflow
            response = await self.arun(
                message=f"Classify complaint: {complaint.content}",
                data=complaint.model_dump()
            )
            
            # Extract result from final step
            if response and hasattr(response, 'content') and response.content:
                result = ClassificationResult(**response.content)
                logger.info(f"Agno workflow classification completed: {result.category}")
                return result
            else:
                raise ValueError("No valid response from workflow")
                
        except Exception as e:
            logger.error(f"Error in Agno workflow classification: {e}")
            # Create fallback result
            fallback_output = await self._create_fallback_result(complaint, e)
            return ClassificationResult(**fallback_output.content)