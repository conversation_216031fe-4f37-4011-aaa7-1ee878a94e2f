# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AI Multi-Agent Complaint Classification System - 一個基於 Agno 框架的智能多代理陳情案件分類系統，採用「專家委員會」架構，通過多個專業化 AI 代理協作實現高準確度和可靠性的陳情案件自動分類。

## Essential Commands

### Installation and Setup

```bash
# Install dependencies using uv
uv sync

# Setup environment variables
# Copy .env.example to .env and configure API keys (OPENAI_API_KEY or ANTHROPIC_API_KEY)
```

### Running Tests

```bash
# Run system validation test
python test_system.py

# Run all unit tests
python -m pytest tests/ -v

# Run specific test files
python -m pytest tests/test_knowledge.py -v
python -m pytest tests/test_classifier.py -v
```

### Development and Usage

```bash
# Classify a complaint via CLI (Legacy workflow)
python -m complaint_classifier.cli classify "路燈不亮了，請派人修理"

# Classify using Agno workflow
USE_AGNO_WORKFLOW=true python -m complaint_classifier.cli classify "路燈不亮了，請派人修理"

# View available categories
python -m complaint_classifier.cli categories

# Validate system setup
python -m complaint_classifier.cli validate

# View current session usage statistics
python -m complaint_classifier.cli usage-stats

# Reset usage statistics
python -m complaint_classifier.cli reset-usage

# Test Agno workflow integration
python test_agno_integration.py
```

### Workflow Selection

The system supports two workflow implementations:

```bash
# Use Agno Workflow (new, recommended)
export USE_AGNO_WORKFLOW=true

# Use Legacy WorkflowEngine (default)
export USE_AGNO_WORKFLOW=false  # or unset
```

## Architecture Overview

### Multi-Agent System

The system uses specialized agents that collaborate through two workflow implementations:

**Agno Workflow (New):**
1. **MainCategoryAnalyzer** - High-level semantic understanding
2. **SubCategoryAnalyzer** - Fine-grained classification
3. **ReviewAgent** - Quality assurance and validation
4. **OutputAgent** - Final result formatting

**Legacy WorkflowEngine:**
1. **TriageAgent** - Fast initial assessment and routing (currently disabled)
2. **MainCategoryAnalyzer** - High-level semantic understanding
3. **SubCategoryAnalyzer** - Fine-grained classification
4. **ReviewAgent** - Quality assurance and validation
5. **OutputAgent** - Final result formatting
6. **OrchestratorAgent** - Workflow coordination

### Workflow Approaches

**Agno Workflow**: Uses declarative workflow management with built-in error handling, retry logic, and session state management. Provides ~70% code reduction while maintaining all functionality.

**Legacy Workflow**: Custom workflow orchestration with manual step coordination and error handling.

### Knowledge Base

-   Located at `src/data/categories_def.json`
-   Contains 21 main categories and 157 sub-categories
-   Dynamically injected into agent contexts to optimize token usage

### Token Usage Tracking

-   **Real-time Monitoring**: Tracks token usage and costs for each API call
-   **Cost Calculation**: Supports OpenAI pricing (gpt-4.1-mini: $0.40/1M input, $1.60/1M output)
-   **Batch Processing**: Cumulative statistics for processing multiple cases
-   **Export Functionality**: JSON/CSV reports with detailed cost analysis

## Key Code Patterns

### Agent Implementation

All agents inherit from `BaseClassificationAgent` in `src/complaint_classifier/agents/base.py` which:

-   Integrates with Agno framework
-   Provides error handling and retry logic
-   Manages context injection

### Configuration

-   Settings managed via Pydantic in `src/complaint_classifier/config.py`
-   Environment variables loaded from `.env` file
-   Model configurations can be customized per agent

### API Entry Point

Main classifier interface is `ComplaintClassifier` in `src/complaint_classifier/classifier.py`:

```python
classifier = ComplaintClassifier()
result = await classifier.classify(content="陳情內容", case_id="CASE001")
```

## Important Files

**Workflow Implementations:**
-   `src/complaint_classifier/workflow/agno_workflow.py` - New Agno-based workflow (recommended)
-   `src/complaint_classifier/workflow/engine.py` - Legacy workflow orchestration

**Core System:**
-   `src/complaint_classifier/classifier.py` - Main classifier interface with workflow selection
-   `src/complaint_classifier/knowledge/context_injector.py` - Smart context injection
-   `src/data/categories_def.json` - Classification knowledge base
-   `src/complaint_classifier/usage/tracker.py` - Token usage tracking core
-   `src/complaint_classifier/usage/batch_reporter.py` - Batch processing and reporting

**Testing and Validation:**
-   `test_system.py` - System validation and testing script
-   `test_agno_integration.py` - Agno workflow integration tests
-   `example_batch_processing.py` - Batch processing demonstration

## Usage Examples

### Single Classification with Usage Tracking

```python
from src.complaint_classifier.classifier import ComplaintClassifier

classifier = ComplaintClassifier()
result = await classifier.classify("路燈不亮了，請派人修理")

# Result includes usage statistics
print(result.usage_summary.get_summary_text())
# Output: 🤖 Token 使用: 輸入 1,200 + 輸出 450 = 總計 1,650 tokens | 費用: $0.001200 USD
```

### Batch Processing with Cost Analysis

```python
from src.complaint_classifier.usage.batch_reporter import BatchReporter

reporter = BatchReporter()
reporter.start_batch()

# Process multiple cases...
for case in cases:
    result = await classifier.classify(case['content'], case['case_id'])
    reporter.add_classification_result(case['case_id'], case['content'], result)

# Generate cost report
summary = reporter.end_batch()
cost_report = reporter.generate_cost_analysis_report(summary)
reporter.export_to_json("batch_results.json", summary)
```

## Notes

-   The system is designed for Chinese complaint classification but architecture supports other languages
-   Uses async/await throughout for concurrent processing
-   Comprehensive error handling with fallback to default category
-   All agent decisions are recorded in the reasoning chain for transparency
