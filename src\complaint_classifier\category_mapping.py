"""
Category mapping utilities for complaint classification.

This module provides utilities to map between main categories and sub categories
based on the categories_def.json structure.
"""

from typing import Dict, List, Set

from .models import MainCategory, SubCategory


# Mapping from main categories to their sub categories
MAIN_TO_SUB_MAPPING: Dict[MainCategory, List[SubCategory]] = {
    MainCategory.TRAFFIC_SIGNALS_SIGNS_LINES_TRANSPORT: [
        SubCategory.BUS_PROBLEMS_STOP_FACILITIES,
        SubCategory.TRAFFIC_SIGNS_LINES_MIRRORS,
        SubCategory.TRAFFIC_SIGNALS_SIGNS_LINES_TRANSPORT_OTHER,
        SubCategory.TRAFFIC_SIGNAL_INSTALLATION_TIMING,
        SubCategory.ROADSIDE_PARKING_ISSUES,
        SubCategory.UBIKE_RENTAL_ISSUES,
        SubCategory.TRAFFIC_SIGNAL_MALFUNCTION,
        SubCategory.TRAFFIC_SIGN_MIRROR_DAMAGE,
        SubCategory.MRT_OPERATION_MANAGEMENT,
        SubCategory.MRT_CONSTRUCTION_ISSUES,
        SubCategory.BUS_DYNAMIC_SYSTEM_ISSUES,
        SubCategory.PARKING_FEE_ISSUES,
        SubCategory.TAXI_ISSUES_STAND_FACILITIES,
        SubCategory.FREE_CITIZEN_BUS_ISSUES,
    ],
    MainCategory.ROAD_OBSTRUCTION_REMOVAL: [
        SubCategory.ROAD_ARCADE_SIDEWALK_OCCUPATION,
        SubCategory.UNLICENSED_ABANDONED_VEHICLE_REPORT,
        SubCategory.LICENSED_ABANDONED_VEHICLE_REPORT,
        SubCategory.ROAD_OBSTRUCTION_REMOVAL_OTHER,
        SubCategory.ADVERTISING_VEHICLE_PARKING_OCCUPATION,
    ],
    MainCategory.NOISE_POLLUTION_ENVIRONMENT: [
        SubCategory.DIRTY_SPOT_REPORT,
        SubCategory.RESIDENTIAL_VEHICLE_NOISE,
        SubCategory.COMPREHENSIVE_ENVIRONMENTAL_POLLUTION,
        SubCategory.NOISE_POLLUTION_ENVIRONMENT_OTHER,
        SubCategory.BUSINESS_FACTORY_CONSTRUCTION_NOISE,
        SubCategory.AIR_POLLUTION,
        SubCategory.RESIDENTIAL_HUMAN_ANIMAL_NOISE,
        SubCategory.GARBAGE_TRUCK_ROUTE_MANAGEMENT,
        SubCategory.FACTORY_WASTEWATER_RIVER_POLLUTION,
        SubCategory.ILLEGAL_ADVERTISING_POSTING,
        SubCategory.WASTE_COLLECTION_APPOINTMENT,
        SubCategory.DOG_CAT_CARCASS_REMOVAL,
    ],
    MainCategory.OTHER_CATEGORY: [
        SubCategory.OTHER_SUGGESTIONS_CONSULTATIONS_COMPLAINTS,
        SubCategory.OTHER_REPORT_CASES,
    ],
    MainCategory.POLICE_TRAFFIC_ENFORCEMENT: [
        SubCategory.POLICE_TRAFFIC_ENFORCEMENT_OTHER,
        SubCategory.POLICE_DISCIPLINE,
        SubCategory.TRAFFIC_TICKET_APPEAL,
        SubCategory.TRAFFIC_GUIDANCE_CONGESTION_REPORT,
        SubCategory.SECURITY_MAINTENANCE,
        SubCategory.MORALITY_VIOLATION,
        SubCategory.RED_LIGHT_SPEED_CAMERA_INSTALLATION,
        SubCategory.SURVEILLANCE_CAMERA_ISSUES,
        SubCategory.VEHICLE_TOWING_DISPUTE,
    ],
    MainCategory.BUILDING_MANAGEMENT: [
        SubCategory.APARTMENT_BUILDING_MANAGEMENT_ISSUES,
        SubCategory.GENERAL_ILLEGAL_CONSTRUCTION_REPORT,
        SubCategory.BUILDING_PUBLIC_SAFETY_ISSUES,
        SubCategory.BUILDING_MANAGEMENT_OTHER,
        SubCategory.UNDER_CONSTRUCTION_ILLEGAL_BUILDING_REPORT,
        SubCategory.BUILDING_REGULATION_ISSUES,
        SubCategory.ILLEGAL_SIGNAGE_ADVERTISING_REPORT,
        SubCategory.SOCIAL_HOUSING_MANAGEMENT,
        SubCategory.LICENSED_CONSTRUCTION_NEIGHBOR_DAMAGE,
        SubCategory.REPORTED_ILLEGAL_BUILDING_DEMOLITION,
    ],
    MainCategory.ROAD_DRAINAGE_MAINTENANCE: [
        SubCategory.ROAD_SURFACE_UNEVEN_HOLES,
        SubCategory.ROAD_DRAINAGE_MAINTENANCE_OTHER,
        SubCategory.ROAD_CONSTRUCTION_TIME_TRAFFIC_SAFETY,
        SubCategory.ROAD_SIDE_DITCH_CLEANING_ODOR,
        SubCategory.DRAINAGE_COVER_REPAIR,
        SubCategory.ROAD_FLOODING_REPORT,
        SubCategory.MANHOLE_COVER_NOISE,
        SubCategory.CABLE_UNDERGROUND_HANGING,
        SubCategory.ROAD_OIL_STAIN_REMOVAL,
    ],
    MainCategory.STREET_LIGHTS_TREES_PARKS: [
        SubCategory.STREET_LIGHTS_TREES_PARKS_OTHER,
        SubCategory.PARK_GREENERY_TREE_MAINTENANCE,
        SubCategory.STREET_LIGHT_MALFUNCTION,
        SubCategory.PARK_FACILITY_DAMAGE,
        SubCategory.TREE_FALLING,
        SubCategory.STREET_LIGHT_INSTALLATION_RELOCATION,
        SubCategory.NEW_PARK_PROPOSAL,
    ],
    MainCategory.HEALTH_ADMINISTRATION: [
        SubCategory.FOOD_SAFETY_HYGIENE,
        SubCategory.DRUG_COSMETIC_MANAGEMENT,
        SubCategory.MEDICAL_MANAGEMENT,
        SubCategory.TOBACCO_CONTROL,
        SubCategory.HEALTH_ADMINISTRATION_OTHER,
        SubCategory.INFECTIOUS_DISEASE_PREVENTION_VACCINATION,
        SubCategory.SUICIDE_PREVENTION_MENTAL_HEALTH,
    ],
    MainCategory.EDUCATION_SPORTS: [
        SubCategory.ELEMENTARY_SCHOOL_ISSUES,
        SubCategory.JUNIOR_HIGH_SCHOOL_ISSUES,
        SubCategory.EDUCATION_SPORTS_OTHER,
        SubCategory.SENIOR_HIGH_SCHOOL_ISSUES,
        SubCategory.CRAM_SCHOOL_ISSUES,
        SubCategory.SPORTS_ACTIVITY_VENUE_MANAGEMENT,
        SubCategory.KINDERGARTEN_ISSUES,
        SubCategory.SPECIAL_EDUCATION_ISSUES,
        SubCategory.TEACHER_TRANSFER_RECRUITMENT,
        SubCategory.COMMUNITY_COLLEGE_LIFELONG_EDUCATION,
        SubCategory.SCHOOL_SPORTS_ISSUES,
    ],
    MainCategory.LABOR_ADMINISTRATION: [
        SubCategory.COMPANY_EMPLOYER_LABOR_LAW_VIOLATION,
        SubCategory.LABOR_LAW_CONSULTATION,
        SubCategory.LABOR_ADMINISTRATION_OTHER,
        SubCategory.LABOR_DISPUTE_COORDINATION,
        SubCategory.MIGRANT_WORKER_AFFAIRS,
        SubCategory.EMPLOYMENT_SERVICE_VOCATIONAL_TRAINING,
        SubCategory.EMPLOYMENT_DISCRIMINATION,
        SubCategory.DISABLED_EMPLOYMENT,
    ],
    MainCategory.COMMERCE_ECONOMY_TAX: [
        SubCategory.STORE_ILLEGAL_OPERATION_REPORT,
        SubCategory.UTILITY_ISSUES,
        SubCategory.TAX_ISSUES,
        SubCategory.COMMERCE_ECONOMY_TAX_OTHER,
        SubCategory.FACTORY_ILLEGAL_OPERATION_REPORT,
        SubCategory.MARKET_VENDOR_MANAGEMENT,
        SubCategory.BUSINESS_REGISTRATION_ISSUES,
        SubCategory.HOTEL_HOMESTAY_ILLEGAL_OPERATION_REPORT,
    ],
    MainCategory.SOCIAL_ASSISTANCE_WELFARE: [
        SubCategory.SOCIAL_ASSISTANCE_WELFARE_OTHER,
        SubCategory.DISABILITY_WELFARE_REHABILITATION_BUS,
        SubCategory.ELDERLY_WELFARE_LONG_TERM_DAY_CARE,
        SubCategory.SOCIAL_ASSISTANCE_LOW_INCOME_EMERGENCY,
        SubCategory.WOMEN_WELFARE_SPECIAL_CIRCUMSTANCES_BIRTH_CHILDCARE,
        SubCategory.WOMEN_CHILDREN_CENTER_CHILDCARE_CENTER,
        SubCategory.CHILDREN_YOUTH_WELFARE_EARLY_INTERVENTION_ASSISTANCE,
        SubCategory.DOMESTIC_VIOLENCE_SEXUAL_ASSAULT_CHILD_PROTECTION,
        SubCategory.HOUSING_RENTAL_SUBSIDY_ISSUES,
        SubCategory.PEOPLE_ORGANIZATION_GUIDANCE,
        SubCategory.FAMILY_SERVICE_CENTER,
    ],
    MainCategory.LAND_ADMINISTRATION: [
        SubCategory.LAND_ILLEGAL_USE_REPORT,
        SubCategory.LAND_ADMINISTRATION_OTHER,
        SubCategory.REAL_ESTATE_TRANSACTION,
        SubCategory.LAND_EXPROPRIATION,
        SubCategory.LAND_BUILDING_REGISTRATION,
        SubCategory.LAND_REZONING,
        SubCategory.LAND_SURVEYING,
        SubCategory.CADASTRAL_MAP_RESURVEY,
    ],
    MainCategory.FIRE_ADMINISTRATION: [
        SubCategory.FIRE_EQUIPMENT_SAFETY_INSPECTION,
        SubCategory.FIRE_LANE_ILLEGAL_CONSTRUCTION_DEBRIS,
        SubCategory.FIRE_ADMINISTRATION_OTHER,
        SubCategory.GAS_CYLINDER_STORAGE_ISSUES,
        SubCategory.FIRE_HYDRANT_INSTALLATION_RELOCATION_SIGNAGE,
    ],
    MainCategory.SERVICE_QUALITY_WEBSITE_APP: [
        SubCategory.THANK_YOU_LETTER,
        SubCategory.GOVERNMENT_WEBSITE_APP_MANAGEMENT,
        SubCategory.SERVICE_ATTITUDE_ISSUES,
        SubCategory.ADMINISTRATIVE_EFFICIENCY_ISSUES,
        SubCategory.PROFESSIONAL_KNOWLEDGE_ISSUES,
        SubCategory.SERVICE_QUALITY_WEBSITE_APP_OTHER,
    ],
    MainCategory.ANIMAL_SHELTER_PROTECTION: [
        SubCategory.ANIMAL_SHELTER_PROTECTION_OTHER,
        SubCategory.ANIMAL_SHELTER_ADOPTION_ISSUES,
        SubCategory.ANIMAL_TRAPPED_INJURED_REPORT,
        SubCategory.BEE_SNAKE_CATCHING,
    ],
    MainCategory.CULTURE_ARTS_LIBRARY: [
        SubCategory.LIBRARY_READING_ROOM_VENUE_MANAGEMENT,
        SubCategory.ARTS_PERFORMANCE_ACTIVITIES,
        SubCategory.CULTURE_ARTS_LIBRARY_OTHER,
        SubCategory.ARTS_VENUE_MANAGEMENT,
        SubCategory.CULTURAL_HERITAGE_ISSUES,
    ],
    MainCategory.CIVIL_AFFAIRS: [
        SubCategory.HOUSEHOLD_REGISTRATION_SERVICE,
        SubCategory.CIVIL_AFFAIRS_OTHER,
        SubCategory.RELIGIOUS_AFFAIRS,
        SubCategory.FUNERAL_CEREMONY,
        SubCategory.MILITARY_SERVICE_ISSUES,
    ],
    MainCategory.POLITICAL_ETHICS: [
        SubCategory.ADMINISTRATIVE_MISCONDUCT,
        SubCategory.OTHER_MALFEASANCE,
        SubCategory.BRIBERY_CORRUPTION,
    ],
    MainCategory.CITIZEN_CARD: [
        SubCategory.CITIZEN_CARD_OTHER,
        SubCategory.CITIZEN_CARD_BENEFITS_VALUE_ADDED_SERVICES,
        SubCategory.CARD_SENSING_USAGE_ISSUES,
        SubCategory.STUDENT_CARD_APPLICATION_ISSUES,
        SubCategory.GENERAL_CARD_APPLICATION_ISSUES,
        SubCategory.MOBILE_CARD_JOINT_CARD_APPLICATION,
        SubCategory.VOLUNTEER_CARD_APPLICATION_ISSUES,
        SubCategory.SENIOR_CARD_APPLICATION_ISSUES,
    ],
}

# Reverse mapping from sub categories to their parent main category
SUB_TO_MAIN_MAPPING: Dict[SubCategory, MainCategory] = {}
for main_cat, sub_cats in MAIN_TO_SUB_MAPPING.items():
    for sub_cat in sub_cats:
        SUB_TO_MAIN_MAPPING[sub_cat] = main_cat


def get_sub_categories_for_main(main_category: MainCategory) -> List[SubCategory]:
    """Get all sub categories for a given main category."""
    return MAIN_TO_SUB_MAPPING.get(main_category, [])


def get_main_category_for_sub(sub_category: SubCategory) -> MainCategory:
    """Get the main category for a given sub category."""
    return SUB_TO_MAIN_MAPPING[sub_category]


def validate_category_relationship(main_category: MainCategory, sub_category: SubCategory) -> bool:
    """Validate that a sub category belongs to the given main category."""
    return sub_category in MAIN_TO_SUB_MAPPING.get(main_category, [])


def get_all_main_categories() -> List[MainCategory]:
    """Get all available main categories."""
    return list(MainCategory)


def get_all_sub_categories() -> List[SubCategory]:
    """Get all available sub categories."""
    return list(SubCategory)


def get_valid_sub_categories_set(main_category: MainCategory) -> Set[SubCategory]:
    """Get a set of valid sub categories for a main category (for faster lookup)."""
    return set(MAIN_TO_SUB_MAPPING.get(main_category, []))
