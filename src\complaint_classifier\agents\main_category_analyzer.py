"""
Main Category Analyzer Agent for high-level semantic understanding.
"""

import logging

from agno.agent import Agent

from ..config import ModelConfig
from ..knowledge.context_injector import ContextInjector
from ..models import (
    ComplaintCase,
    MainCategory,
    MainCategoryCandidate,
    MainCategoryResult,
)
from .base import BaseClassificationAgent

logger = logging.getLogger(__name__)


class MainCategoryAnalyzer(BaseClassificationAgent):
    """
    Main Category Analyzer Agent - High-level semantic understanding specialist.

    Responsibilities:
    - Analyze complaints from a macro perspective
    - Determine the core domain/field of the complaint
    - Provide 1-3 most likely main category candidates
    - Focus on high-level semantic understanding
    """

    def __init__(self, model_config: ModelConfig, context_injector: ContextInjector):
        """Initialize the Main Category Analyzer.

        Args:
            model_config: Configuration for the AI model
            context_injector: Context injector for knowledge base access
        """
        super().__init__(
            model_config=model_config,
            name="Main Category Analyzer",
            role="High-level semantic analysis and main category classification specialist",
            response_model=MainCategoryResult,
        )
        self.context_injector = context_injector

    def _get_system_instructions(self) -> list[str]:
        """Get system instructions for the Main Category Analyzer."""
        return [
            "你是一位資深的市政治理專家，專精於高層次語意理解。",
            "你的任務是從宏觀角度判斷市民陳情案件的核心歸屬領域。",
            "你需要分析案件的主要訴求和核心問題，判斷其屬於哪個主要業務領域。",
            "請從提供的主案類清單中選出最相關的 1-3 個候選，按信心度排序。",
            "每個候選都要包含 main_category、confidence、reasoning 和 keywords_found。",
            "confidence 分數應該反映你對該分類的信心程度（0.0-1.0）。",
            "reasoning 應該詳細說明為什麼選擇這個主案類。",
            "keywords_found 應該列出在案件中發現的關鍵詞。",
            "你必須以 JSON 格式回應，包含 candidates 陣列。",
        ]

    def _create_agent(self) -> Agent:
        """Create the Main Category Analyzer Agent."""
        additional_instructions = [
            "回應格式範例：",
            '{"candidates": [',
            '  {"main_category": "道路、水溝維護", "confidence": 0.9, "reasoning": "案件核心圍繞路面破損與積水問題。", "keywords_found": ["路面不平", "破洞", "積水"]},',
            '  {"main_category": "噪音、污染及環境維護", "confidence": 0.4, "reasoning": "雖然提到油漬，但主要訴求是道路結構安全，非環境清潔。", "keywords_found": ["油漬"]}',
            "]}",
        ]

        return self._create_base_agent(additional_instructions)

    async def _process_internal(
        self, complaint: ComplaintCase, **kwargs
    ) -> MainCategoryResult:
        """Process complaint for main category analysis.

        Args:
            complaint: The complaint case to process
            **kwargs: Additional arguments

        Returns:
            Main category analysis result
        """
        # Get context for main categories
        context = self.context_injector.create_main_category_context()

        # Create prompt with injected context
        prompt = self._create_analysis_prompt(complaint.content, context)

        # Get response from agent with structured output
        response = await self.agent.arun(prompt)
        
        # Store the response for metrics tracking
        self._last_agent_response = response

        # With response_model, the response.content should already be a MainCategoryResult
        if isinstance(response.content, MainCategoryResult):
            return response.content

        # Fallback: try to create MainCategoryResult from response content
        try:
            if hasattr(response.content, "model_dump"):
                # If it's a Pydantic model, convert to dict first
                result_data = response.content.model_dump()
            elif isinstance(response.content, dict):
                result_data = response.content
            else:
                # Last resort: try to parse as JSON
                from ..utils import parse_json_response

                result_data = parse_json_response(str(response.content))

            if result_data:
                return MainCategoryResult(**result_data)
        except (TypeError, ValueError) as e:
            logger.error(f"Failed to create MainCategoryResult from response: {e}")

        # Fallback to a default response
        logger.error(
            f"Failed to parse main category analysis response: {response.content}"
        )

        return MainCategoryResult(
            candidates=[
                MainCategoryCandidate(
                    category=MainCategory.OTHER_CATEGORY,
                    confidence=0.1,
                    reasoning="解析回應失敗，使用預設分類",
                    keywords_found=[],
                )
            ]
        )

    def _create_analysis_prompt(self, complaint_content: str, context) -> str:
        """Create the prompt for main category analysis.

        Args:
            complaint_content: The complaint text content
            context: Context data with main category definitions

        Returns:
            Formatted prompt string
        """
        # Format the main category definitions
        category_section = self.context_injector.format_main_category_prompt_section(
            context
        )

        return f"""你是一位資深的市政治理專家。請根據以下市民陳情內容，從下列主案類清單中選出最相關的 1-3 個候選。每個主案類都附有其官方定義以供你精準判斷。

{category_section}

**市民陳情內容:**
{complaint_content}

**分析要求:**
1. 仔細分析陳情內容的核心問題和主要訴求
2. 從上述主案類中選擇最相關的 1-3 個候選
3. 按信心度由高到低排序
4. 為每個候選提供詳細的判斷理由
5. 列出在案件中發現的關鍵詞

請以 JSON 格式回傳你的分析結果，包含 candidates 陣列。"""
