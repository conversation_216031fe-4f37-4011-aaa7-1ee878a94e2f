"""
Centralized logging configuration for the complaint classifier system.
"""

import logging
import sys
from typing import Optional


def setup_logging(
    level: str = "INFO",
    format_string: Optional[str] = None,
    include_timestamp: bool = True
) -> None:
    """
    Setup logging configuration for the entire complaint classifier system.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_string: Custom format string. If None, uses default format
        include_timestamp: Whether to include timestamp in log messages
    """
    
    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Default format string
    if format_string is None:
        if include_timestamp:
            format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        else:
            format_string = "%(name)s - %(levelname)s - %(message)s"
    
    # Configure root logger
    logging.basicConfig(
        level=numeric_level,
        format=format_string,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ],
        force=True  # Override any existing configuration
    )
    
    # Set specific loggers to the same level
    complaint_logger = logging.getLogger("complaint_classifier")
    complaint_logger.setLevel(numeric_level)
    
    # Also set the agno logger level if it exists
    agno_logger = logging.getLogger("agno")
    agno_logger.setLevel(logging.WARNING)  # Keep agno less verbose
    
    logging.getLogger(__name__).info(f"Logging configured with level: {level}")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


def set_log_level(level: str) -> None:
    """
    Dynamically change the logging level.
    
    Args:
        level: New logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Update root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Update handlers
    for handler in root_logger.handlers:
        handler.setLevel(numeric_level)
    
    # Update complaint classifier logger
    complaint_logger = logging.getLogger("complaint_classifier")
    complaint_logger.setLevel(numeric_level)
    
    logging.getLogger(__name__).info(f"Log level changed to: {level}")