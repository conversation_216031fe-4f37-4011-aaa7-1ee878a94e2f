[project]
name = "ai-application"
version = "0.1.0"
description = "AI Multi-Agent System for Citizen Complaint Classification"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "agno>=1.7.6",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0",
    "rich>=13.0.0",
    "typer>=0.12.0",
    "pytest>=8.0.0",
    "pytest-asyncio>=0.24.0",
    "openai>=1.0.0",
    "anthropic>=0.25.0",
    "sqlalchemy>=2.0.42",
]

[tool.pytest.ini_options]
pythonpath = [
  "src"
]
