"""
Workflow engine for managing the complaint classification process.
"""

import logging

from ..agents import (
    MainCategoryAnalyzer,
    OrchestratorAgent,
    OutputAgent,
    ReviewAgent,
    SubCategoryAnalyzer,
    TriageAgent,
)
from ..category_mapping import get_main_category_for_sub
from ..config import Settings
from ..knowledge.context_injector import ContextInjector
from ..knowledge.manager import KnowledgeManager
from ..models import (
    ActionType,
    ClassificationResult,
    ComplaintCase,
    DecisionType,
    MainCategory,
    MainCategoryResult,
    ReviewResult,
    SubCategory,
    SubCategoryResult,
    TriageResult,
)

logger = logging.getLogger(__name__)


class WorkflowEngine:
    """
    Main workflow engine that coordinates the complaint classification process.

    Implements two pathways:
    1. Fast Track: Direct classification for simple, clear cases
    2. Expert Review: Multi-agent analysis for complex cases
    """

    def __init__(self, settings: Settings):
        """Initialize the workflow engine.

        Args:
            settings: Application settings
        """
        self.settings = settings

        # Initialize knowledge management
        self.knowledge_manager = KnowledgeManager(settings.knowledge_base_path)
        self.context_injector = ContextInjector(self.knowledge_manager)

        # Initialize agents
        self._init_agents()

        logger.info("Workflow engine initialized")

    def _init_agents(self):
        """Initialize all agents with their configurations."""
        self.triage_agent = TriageAgent(
            self.settings.agents.triage_model, self.context_injector
        )
        self.orchestrator = OrchestratorAgent(self.settings.agents.orchestrator_model)
        self.main_analyzer = MainCategoryAnalyzer(
            self.settings.agents.main_analyzer_model, self.context_injector
        )
        self.sub_analyzer = SubCategoryAnalyzer(
            self.settings.agents.sub_analyzer_model, self.context_injector
        )
        self.review_agent = ReviewAgent(
            self.settings.agents.review_model, self.context_injector
        )
        self.output_agent = OutputAgent(self.settings.agents.orchestrator_model)

        logger.debug("All agents initialized")

    async def classify_complaint(
        self, complaint: ComplaintCase
    ) -> ClassificationResult:
        """Classify a complaint using the appropriate workflow path.

        Args:
            complaint: The complaint case to classify

        Returns:
            Classification result
        """
        # Clear previous reasoning chain
        self.orchestrator.clear_reasoning_chain()

        try:
            # # Step 1: Triage
            # logger.info(f"Starting classification for case: {complaint.case_id}")
            # triage_result = await self._run_triage(complaint)

            # # Step 2: Route based on triage decision
            # if (
            #     triage_result.action == ActionType.FINALIZE
            #     and self.settings.workflow.enable_fast_track
            # ):
            #     # Fast Track pathway
            #     logger.info("Taking Fast Track pathway")
            #     result = await self._fast_track_classification(complaint, triage_result)
            # else:
            #     # Expert Review pathway
            #     logger.info("Taking Expert Review pathway")
            #     result = await self._expert_review_classification(complaint)

            # Step 1: Directly take Expert Review pathway
            logger.info("Taking Expert Review pathway")
            result = await self._expert_review_classification(complaint)

            logger.info(f"Classification completed: {result.category}")
            return result

        except Exception as e:
            logger.error(f"Error during classification: {e}")
            return await self._handle_classification_error(complaint, e)

    async def _run_triage(self, complaint: ComplaintCase) -> TriageResult:
        """Run the triage process.

        Args:
            complaint: The complaint case

        Returns:
            Triage result
        """
        try:
            triage_result = await self.triage_agent.process(complaint)

            self.orchestrator.add_reasoning_step(
                agent_name="TriageAgent",
                input_data=complaint,
                output_data=triage_result,
                metadata={"step": "triage"},
            )

            return triage_result

        except Exception as e:
            self.orchestrator.handle_error("TriageAgent", e, complaint)
            # Fallback to expert review on triage failure
            return TriageResult(
                action=ActionType.ESCALATE,
                category=None,
                confidence=0.0,
                reasoning=f"Triage failed, escalating to expert review: {str(e)}",
            )

    async def _fast_track_classification(
        self, complaint: ComplaintCase, triage_result: TriageResult
    ) -> ClassificationResult:
        """Handle fast track classification.

        Args:
            complaint: The complaint case
            triage_result: Result from triage

        Returns:
            Classification result
        """
        # Validate the triage category
        if not self._validate_category(triage_result.category):
            logger.warning(
                f"Invalid fast track category: {triage_result.category}, escalating"
            )
            return await self._expert_review_classification(complaint)

        # Get main category from sub category
        main_category = get_main_category_for_sub(triage_result.category)

        # Create classification result
        classification_data = {
            "category": triage_result.category,
            "main_category": main_category,
            "confidence": triage_result.confidence,
            "path_taken": "fast_track",
            "reasoning_chain": self.orchestrator.get_reasoning_chain(),
            "review_passed": None,  # No review in fast track
            "main_category_reasoning": f"快速通道分類：主類別為【{main_category.value}】",
            "sub_category_reasoning": triage_result.reasoning,
            "classification_summary": f"案件經快速通道分類為【{main_category.value}】下的【{triage_result.category.value}】。{triage_result.reasoning}",
        }

        result = await self.output_agent.process(classification_data)

        self.orchestrator.add_reasoning_step(
            agent_name="OutputAgent",
            input_data=classification_data,
            output_data=result,
            metadata={"step": "output", "path": "fast_track"},
        )

        return result

    async def _expert_review_classification(
        self, complaint: ComplaintCase
    ) -> ClassificationResult:
        """Handle expert review classification.

        Args:
            complaint: The complaint case

        Returns:
            Classification result
        """
        max_retries = 2
        retry_count = 0

        while retry_count <= max_retries:
            try:
                # Step 1: Main category analysis
                main_result = await self._run_main_category_analysis(complaint)

                # Step 2: Sub-category analysis
                main_category = (
                    main_result.candidates[0].category
                    if main_result.candidates
                    else None
                )
                if not main_category:
                    raise ValueError("No main category candidates found")

                sub_result = await self._run_sub_category_analysis(
                    complaint, main_category
                )

                # Step 3: Review
                review_result = await self._run_review(
                    complaint, main_result, sub_result
                )

                # Step 4: Handle review result
                if (
                    review_result.decision == DecisionType.PASS
                    or retry_count >= max_retries
                ):
                    # Accept the classification or we've exhausted retries
                    sub_category = (
                        sub_result.candidates[0].category
                        if sub_result.candidates
                        else None
                    )
                    final_category = (
                        sub_category or self.settings.workflow.fallback_category
                    )

                    # Extract reasoning from results
                    main_category_reasoning = (
                        main_result.candidates[0].reasoning
                        if main_result.candidates
                        else "無法判斷主類別"
                    )
                    sub_category_reasoning = (
                        sub_result.candidates[0].reasoning
                        if sub_result.candidates
                        else "無法判斷子類別"
                    )

                    # Generate classification summary
                    classification_summary = f"經專家委員會分析，案件屬於【{main_category.value}】主類別下的【{final_category.value}】子類別。"
                    if (
                        main_result.candidates
                        and main_result.candidates[0].keywords_found
                    ):
                        classification_summary += f"關鍵詞：{', '.join(main_result.candidates[0].keywords_found)}。"
                    if (
                        sub_result.candidates
                        and sub_result.candidates[0].evidence_from_text
                    ):
                        classification_summary += (
                            f"案件證據：{sub_result.candidates[0].evidence_from_text}。"
                        )

                    classification_data = {
                        "category": final_category,
                        "main_category": main_category,
                        "confidence": sub_result.candidates[0].confidence
                        if sub_result.candidates
                        else 0.0,
                        "path_taken": "expert_review",
                        "reasoning_chain": self.orchestrator.get_reasoning_chain(),
                        "review_passed": review_result.decision == DecisionType.PASS,
                        "main_category_reasoning": main_category_reasoning,
                        "sub_category_reasoning": sub_category_reasoning,
                        "classification_summary": classification_summary,
                    }

                    result = await self.output_agent.process(classification_data)

                    self.orchestrator.add_reasoning_step(
                        agent_name="OutputAgent",
                        input_data=classification_data,
                        output_data=result,
                        metadata={
                            "step": "output",
                            "path": "expert_review",
                            "retry_count": retry_count,
                        },
                    )

                    return result
                else:
                    # Review failed, retry
                    retry_count += 1
                    logger.warning(
                        f"Review failed, retrying ({retry_count}/{max_retries})"
                    )

            except Exception as e:
                retry_count += 1
                logger.error(f"Error in expert review (attempt {retry_count}): {e}")
                if retry_count > max_retries:
                    return await self._handle_classification_error(complaint, e)

        # This should not be reached, but just in case
        return await self._handle_classification_error(
            complaint, Exception("Max retries exceeded")
        )

    async def _run_main_category_analysis(
        self, complaint: ComplaintCase
    ) -> MainCategoryResult:
        """Run main category analysis.

        Args:
            complaint: The complaint case

        Returns:
            Main category analysis result
        """
        try:
            main_result = await self.main_analyzer.process(complaint)

            self.orchestrator.add_reasoning_step(
                agent_name="MainCategoryAnalyzer",
                input_data=complaint,
                output_data=main_result,
                metadata={"step": "main_category_analysis"},
            )

            return main_result

        except Exception as e:
            self.orchestrator.handle_error("MainCategoryAnalyzer", e, complaint)
            raise

    async def _run_sub_category_analysis(
        self, complaint: ComplaintCase, main_category: MainCategory
    ) -> SubCategoryResult:
        """Run sub-category analysis.

        Args:
            complaint: The complaint case
            main_category: The selected main category

        Returns:
            Sub-category analysis result
        """
        try:
            sub_result = await self.sub_analyzer.process(
                complaint, main_category=main_category
            )

            self.orchestrator.add_reasoning_step(
                agent_name="SubCategoryAnalyzer",
                input_data={"complaint": complaint, "main_category": main_category},
                output_data=sub_result,
                metadata={"step": "sub_category_analysis"},
            )

            return sub_result

        except Exception as e:
            self.orchestrator.handle_error(
                "SubCategoryAnalyzer",
                e,
                {"complaint": complaint, "main_category": main_category},
            )
            raise

    async def _run_review(
        self,
        complaint: ComplaintCase,
        main_result: MainCategoryResult,
        sub_result: SubCategoryResult,
    ) -> ReviewResult:
        """Run the review process.

        Args:
            complaint: The complaint case
            main_result: Main category analysis result
            sub_result: Sub-category analysis result

        Returns:
            Review result
        """
        try:
            review_result = await self.review_agent.process(
                complaint, main_result=main_result, sub_result=sub_result
            )

            self.orchestrator.add_reasoning_step(
                agent_name="ReviewAgent",
                input_data={
                    "complaint": complaint,
                    "main_result": main_result,
                    "sub_result": sub_result,
                },
                output_data=review_result,
                metadata={"step": "review"},
            )

            return review_result

        except Exception as e:
            self.orchestrator.handle_error(
                "ReviewAgent",
                e,
                {
                    "complaint": complaint,
                    "main_result": main_result,
                    "sub_result": sub_result,
                },
            )
            raise

    def _validate_category(self, category: str) -> bool:
        """Validate if a category exists in the knowledge base.

        Args:
            category: Category to validate

        Returns:
            True if valid, False otherwise
        """
        if not category:
            return False

        try:
            # Check if it's a valid sub-category
            for main_cat in self.knowledge_manager.knowledge_base.get_main_categories():
                sub_cats = self.knowledge_manager.knowledge_base.get_sub_categories(
                    main_cat
                )
                if category in sub_cats:
                    return True
            return False
        except Exception:
            return False

    async def _handle_classification_error(
        self, complaint: ComplaintCase, error: Exception
    ) -> ClassificationResult:
        """Handle classification errors with fallback.

        Args:
            complaint: The complaint case
            error: The error that occurred

        Returns:
            Fallback classification result
        """
        logger.error(f"Classification failed, using fallback: {error}")

        # Get main category for fallback subcategory
        fallback_main_category = get_main_category_for_sub(
            SubCategory.OTHER_SUGGESTIONS_CONSULTATIONS_COMPLAINTS
        )

        classification_data = {
            "category": SubCategory.OTHER_SUGGESTIONS_CONSULTATIONS_COMPLAINTS,
            "main_category": fallback_main_category,
            "confidence": 0.0,
            "path_taken": "expert_review",
            "reasoning_chain": self.orchestrator.get_reasoning_chain(),
            "review_passed": False,
            "main_category_reasoning": f"分類過程發生錯誤，預設分類為【{fallback_main_category.value}】",
            "sub_category_reasoning": f"由於系統錯誤，使用預設分類：{str(error)}",
            "classification_summary": f"分類過程發生錯誤，已使用預設分類【{fallback_main_category.value}】下的【其他建議、諮詢或陳情】。",
        }

        return await self.output_agent.process(classification_data)
