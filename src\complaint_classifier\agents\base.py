"""
Base agent class for the complaint classification system.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Optional, Type

from agno.agent import Agent
from agno.models.anthropic import Claude
from agno.models.openai import OpenAIChat
from pydantic import BaseModel

from ..config import ModelConfig
from ..usage.tracker import TokenUsage, get_usage_tracker

logger = logging.getLogger(__name__)


class BaseClassificationAgent(ABC):
    """Base class for all classification agents."""

    def __init__(
        self,
        model_config: ModelConfig,
        name: str,
        role: str,
        response_model: Optional[Type[BaseModel]] = None,
    ):
        """Initialize the base agent.

        Args:
            model_config: Configuration for the AI model
            name: Name of the agent
            role: Role description of the agent
            response_model: Pydantic model for structured outputs
        """
        self.model_config = model_config
        self.name = name
        self.role = role
        self.response_model = response_model
        self._agent: Optional[Agent] = None

    def _create_model(self):
        """Create the appropriate model based on configuration."""
        if self.model_config.provider.lower() == "openai":
            return OpenAIChat(
                id=self.model_config.model_id,
                temperature=self.model_config.temperature,
                max_tokens=self.model_config.max_tokens,
                api_key=self.model_config.api_key,
            )
        elif self.model_config.provider.lower() == "anthropic":
            return Claude(
                id=self.model_config.model_id,
                temperature=self.model_config.temperature,
                max_tokens=self.model_config.max_tokens,
                api_key=self.model_config.api_key,
            )
        else:
            raise ValueError(
                f"Unsupported model provider: {self.model_config.provider}"
            )

    @property
    def agent(self) -> Agent:
        """Get the Agno agent instance, creating it if necessary."""
        if self._agent is None:
            self._agent = self._create_agent()
        return self._agent

    @abstractmethod
    def _create_agent(self) -> Agent:
        """Create the Agno agent with specific configuration.

        Returns:
            Configured Agno agent
        """
        pass

    @abstractmethod
    def _get_system_instructions(self) -> list[str]:
        """Get system instructions for this agent.

        Returns:
            List of instruction strings
        """
        pass

    def _create_base_agent(
        self, additional_instructions: Optional[list[str]] = None
    ) -> Agent:
        """Create a base Agno agent with common configuration.

        Args:
            additional_instructions: Additional instructions specific to the agent

        Returns:
            Configured Agno agent
        """
        instructions = self._get_system_instructions()
        if additional_instructions:
            instructions.extend(additional_instructions)

        model = self._create_model()

        # Create agent with response_model if specified
        agent_kwargs = {
            "name": self.name,
            "role": self.role,
            "model": model,
            "instructions": instructions,
            "markdown": False,
            "show_tool_calls": False,
            "structured_outputs": True,
            "telemetry": False,  # 關閉最小化遙測
            "monitoring": False,  # 關閉監控上報
        }

        # Add response_model if specified
        if self.response_model:
            agent_kwargs["response_model"] = self.response_model

        return Agent(**agent_kwargs)

    async def process(self, input_data: Any, **kwargs) -> Any:
        """Process input data and return result with usage tracking.

        Args:
            input_data: Input data to process
            **kwargs: Additional keyword arguments

        Returns:
            Processing result
        """
        try:
            logger.debug(f"{self.name} processing input")
            
            # Store the last response for metrics tracking
            self._last_agent_response = None
            
            result = await self._process_internal(input_data, **kwargs)

            # Track usage after successful processing
            self._track_usage_if_available(result)

            logger.debug(f"{self.name} completed processing")
            return result
        except Exception as e:
            logger.error(f"Error in {self.name}: {e}")
            raise

    def _track_usage_if_available(self, result: Any) -> Optional[TokenUsage]:
        """Track usage from the agent's last response if available.

        Args:
            result: The processing result

        Returns:
            TokenUsage if tracked, None otherwise
        """
        try:
            # Get the last response - first try from stored response, then from agent
            last_response = None
            
            if hasattr(self, "_last_agent_response") and self._last_agent_response:
                last_response = self._last_agent_response
                logger.debug(f"{self.name} - Using stored response")
            elif (
                hasattr(self, "_agent")
                and self._agent
                and hasattr(self._agent, "last_response")
            ):
                last_response = self._agent.last_response
                logger.debug(f"{self.name} - Using agent.last_response")
            
            if last_response:
                logger.debug(f"{self.name} - last_response type: {type(last_response)}")

                # Try different ways to get usage/metrics data
                metrics = None
                
                # Method 1: Check for metrics attribute
                if hasattr(last_response, "metrics") and last_response.metrics:
                    raw_metrics = last_response.metrics
                    logger.debug(f"{self.name} - Found metrics in last_response.metrics: {raw_metrics}")
                    
                    # Handle Agno metrics format (values are lists)
                    if isinstance(raw_metrics, dict):
                        metrics = {}
                        # Extract the first value from each list
                        metrics["input_tokens"] = raw_metrics.get("input_tokens", [0])[0] if isinstance(raw_metrics.get("input_tokens"), list) else raw_metrics.get("input_tokens", 0)
                        metrics["output_tokens"] = raw_metrics.get("output_tokens", [0])[0] if isinstance(raw_metrics.get("output_tokens"), list) else raw_metrics.get("output_tokens", 0)
                        metrics["total_tokens"] = raw_metrics.get("total_tokens", [0])[0] if isinstance(raw_metrics.get("total_tokens"), list) else raw_metrics.get("total_tokens", 0)
                        metrics["prompt_tokens"] = raw_metrics.get("prompt_tokens", [0])[0] if isinstance(raw_metrics.get("prompt_tokens"), list) else raw_metrics.get("prompt_tokens", 0)
                        metrics["completion_tokens"] = raw_metrics.get("completion_tokens", [0])[0] if isinstance(raw_metrics.get("completion_tokens"), list) else raw_metrics.get("completion_tokens", 0)
                        metrics["time"] = raw_metrics.get("time", [0])[0] if isinstance(raw_metrics.get("time"), list) else raw_metrics.get("time", 0)
                
                # Method 2: Check for usage attribute (OpenAI style)
                elif hasattr(last_response, "usage") and last_response.usage:
                    usage_data = last_response.usage
                    logger.debug(f"{self.name} - Found usage data: {usage_data}")
                    
                    # Convert OpenAI usage format to our metrics format
                    if hasattr(usage_data, "prompt_tokens") and hasattr(usage_data, "completion_tokens"):
                        metrics = {
                            "input_tokens": usage_data.prompt_tokens,
                            "output_tokens": usage_data.completion_tokens,
                            "total_tokens": usage_data.total_tokens if hasattr(usage_data, "total_tokens") else (usage_data.prompt_tokens + usage_data.completion_tokens),
                            "prompt_tokens": usage_data.prompt_tokens,
                            "completion_tokens": usage_data.completion_tokens,
                        }
                    elif isinstance(usage_data, dict):
                        metrics = {
                            "input_tokens": usage_data.get("prompt_tokens", 0),
                            "output_tokens": usage_data.get("completion_tokens", 0),
                            "total_tokens": usage_data.get("total_tokens", 0),
                            "prompt_tokens": usage_data.get("prompt_tokens", 0),
                            "completion_tokens": usage_data.get("completion_tokens", 0),
                        }
                
                # Method 3: Check if the response itself has usage info
                elif hasattr(result, "usage"):
                    logger.debug(f"{self.name} - Found usage in result: {result.usage}")
                    # Handle if result has usage info
                    pass

                # If we found metrics, track them
                if metrics:
                    # Track usage using the global tracker
                    tracker = get_usage_tracker()
                    usage = tracker.track_usage(
                        model=self.model_config.model_id,
                        provider=self.model_config.provider,
                        metrics=metrics,
                    )

                    logger.info(
                        f"{self.name} usage tracked: {usage.total_tokens} tokens, ${usage.total_cost}"
                    )
                    return usage
                else:
                    logger.warning(f"{self.name} - No metrics/usage data found in response")

            return None

        except Exception as e:
            # Don't fail the main process if usage tracking fails
            logger.warning(f"Failed to track usage for {self.name}: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return None

    @abstractmethod
    async def _process_internal(self, input_data: Any, **kwargs) -> Any:
        """Internal processing method to be implemented by subclasses.

        Args:
            input_data: Input data to process
            **kwargs: Additional keyword arguments

        Returns:
            Processing result
        """
        pass
