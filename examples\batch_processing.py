#!/usr/bin/env python3
"""
Example script demonstrating batch processing with usage tracking.
This script shows how to process multiple complaint cases and generate cost reports.
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path

from src.complaint_classifier.classifier import ComplaintClassifier
from src.complaint_classifier.usage.batch_reporter import BatchReporter
from src.complaint_classifier.usage.tracker import get_usage_tracker

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def process_batch_complaints():
    """Process a batch of complaint cases and generate cost reports."""
    
    # Sample complaint cases
    complaint_cases = [
        {
            "case_id": "CASE_001",
            "content": "路燈不亮了，已經好幾天了，請派人來修理"
        },
        {
            "case_id": "CASE_002", 
            "content": "公車站牌被撞倒了，需要重新立起來"
        },
        {
            "case_id": "CASE_003",
            "content": "垃圾車今天沒有來收垃圾，請確認清運時間"
        },
        {
            "case_id": "CASE_004",
            "content": "公園裡的遊樂設施壞了，小朋友無法使用"
        },
        {
            "case_id": "CASE_005",
            "content": "道路有大坑洞，影響行車安全"
        }
    ]
    
    print("🚀 開始批次處理陳情案件")
    print(f"總案件數: {len(complaint_cases)}")
    print("="*60)
    
    # Initialize classifier
    classifier = ComplaintClassifier()
    
    # Initialize batch reporter
    tracker = get_usage_tracker()
    reporter = BatchReporter(tracker)
    
    # Start batch tracking
    reporter.start_batch()
    
    # Process each case
    results = []
    for i, case in enumerate(complaint_cases, 1):
        print(f"\n處理案件 {i}/{len(complaint_cases)}: {case['case_id']}")
        print(f"內容: {case['content']}")
        
        try:
            # Record start time
            start_time = datetime.now()
            
            # Classify the complaint
            result = await classifier.classify(
                content=case['content'],
                case_id=case['case_id']
            )
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Add to batch reporter
            reporter.add_classification_result(
                case['case_id'],
                case['content'],
                result,
                processing_time
            )
            
            # Display individual result
            print(f"✅ 分類結果: 【{result.main_category.value}】-> 【{result.category.value}】")
            if result.usage_summary:
                print(f"💰 使用統計: {result.usage_summary.get_summary_text()}")
            
            results.append({
                'case': case,
                'result': result,
                'processing_time': processing_time
            })
            
        except Exception as e:
            print(f"❌ 處理失敗: {e}")
            continue
    
    print("\n" + "="*60)
    print("🎉 批次處理完成！")
    
    # End batch and get summary
    batch_summary = reporter.end_batch()
    
    # Generate and display cost analysis report
    print("\n📊 成本分析報告:")
    print("="*60)
    cost_report = reporter.generate_cost_analysis_report(batch_summary)
    print(cost_report)
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    try:
        # Export to JSON
        json_file = f"batch_results_{timestamp}.json"
        reporter.export_to_json(json_file, batch_summary)
        print(f"📄 JSON 報告已匯出: {json_file}")
        
        # Export to CSV
        csv_file = f"batch_results_{timestamp}.csv"
        reporter.export_to_csv(csv_file, batch_summary)
        print(f"📊 CSV 報告已匯出: {csv_file}")
        
    except Exception as e:
        print(f"❌ 匯出失敗: {e}")
    
    # Display summary statistics
    print(f"\n🎯 處理摘要:")
    print(f"   成功處理: {len(results)} / {len(complaint_cases)} 案件")
    print(f"   總費用: ${batch_summary['usage_summary']['total_cost_usd']} USD")
    print(f"   平均每案費用: ${float(batch_summary['usage_summary']['total_cost_usd']) / len(results):.6f} USD")
    
    return batch_summary


async def demonstrate_cost_projections():
    """Demonstrate cost projections for larger batches."""
    print("\n" + "="*60)
    print("📈 成本預測演示")
    print("="*60)
    
    # Get current session usage for projection
    tracker = get_usage_tracker()
    session_summary = tracker.get_session_summary()
    
    if session_summary.total_requests == 0:
        print("⚠️  無使用記錄，無法進行成本預測")
        return
    
    # Calculate average cost per request
    avg_cost_per_request = session_summary.total_cost / session_summary.total_requests
    avg_tokens_per_request = session_summary.total_tokens / session_summary.total_requests
    
    print(f"基於當前 {session_summary.total_requests} 次請求的數據:")
    print(f"   平均每次請求費用: ${avg_cost_per_request:.6f} USD")
    print(f"   平均每次請求 Token: {avg_tokens_per_request:.0f}")
    print()
    
    # Project costs for different scales
    scales = [10, 50, 100, 500, 1000, 5000]
    
    print("💰 不同規模的成本預測:")
    print("-" * 40)
    print(f"{'案件數':<8} {'預估費用 (USD)':<15} {'預估 Token':<15}")
    print("-" * 40)
    
    for scale in scales:
        projected_cost = avg_cost_per_request * scale
        projected_tokens = avg_tokens_per_request * scale
        print(f"{scale:<8} ${projected_cost:<14.6f} {projected_tokens:<14,.0f}")
    
    print("-" * 40)


def main():
    """Main execution function."""
    try:
        # Run the batch processing demo
        batch_summary = asyncio.run(process_batch_complaints())
        
        # Show cost projections
        asyncio.run(demonstrate_cost_projections())
        
        print("\n✅ 示例完成！")
        print("💡 提示: 您可以檢查生成的 JSON 和 CSV 檔案來查看詳細結果")
        
    except KeyboardInterrupt:
        print("\n⏹️  處理被用戶中斷")
    except Exception as e:
        print(f"\n❌ 執行錯誤: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()