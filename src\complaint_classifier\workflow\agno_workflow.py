"""
Agno Workflow implementation for complaint classification.

This module implements the complaint classification workflow using Agno's
official Workflow class, providing a pure Python workflow implementation
with proper state management and error handling.
"""

import logging
import asyncio
import concurrent.futures
from typing import Any, Dict, Iterator

from agno.storage.sqlite import SqliteStorage
from agno.workflow import Workflow, RunResponse

from ..agents import (
    MainCategoryAnalyzer,
    OutputAgent,
    ReviewAgent,
    SubCategoryAnalyzer,
)
from ..category_mapping import get_main_category_for_sub
from ..config import Settings
from ..knowledge.context_injector import ContextInjector
from ..knowledge.manager import KnowledgeManager
from ..models import (
    ClassificationResult,
    ComplaintCase,
    DecisionType,
    MainCategoryResult,
    ReviewResult,
    SubCategory,
    SubCategoryResult,
)

logger = logging.getLogger(__name__)


class ComplaintClassificationWorkflow(Workflow):
    """
    Agno-based complaint classification workflow.

    This workflow uses Agno's standard Workflow class with a pure Python
    run method for maximum flexibility and control.
    """

    def __init__(self, settings: Settings):
        """Initialize the Agno workflow.

        Args:
            settings: Application settings
        """
        self.settings = settings

        # Initialize knowledge management
        self.knowledge_manager = KnowledgeManager(settings.knowledge_base_path)
        self.context_injector = ContextInjector(self.knowledge_manager)

        # Initialize agents
        self._init_agents()

        # Initialize parent Workflow with correct parameters
        super().__init__(
            name="AI Complaint Classification Expert System",
            description="Multi-agent expert committee for intelligent complaint classification using specialized AI agents",
            storage=self._create_storage(),
            session_state={},  # 使用 session_state 進行狀態管理
            debug_mode=True,
        )

        logger.info("Agno Complaint Classification Workflow initialized")

    def _init_agents(self):
        """Initialize all agents with their configurations."""
        self.main_analyzer = MainCategoryAnalyzer(
            self.settings.agents.main_analyzer_model, self.context_injector
        )
        self.sub_analyzer = SubCategoryAnalyzer(
            self.settings.agents.sub_analyzer_model, self.context_injector
        )
        self.review_agent = ReviewAgent(
            self.settings.agents.review_model, self.context_injector
        )
        self.output_agent = OutputAgent(self.settings.agents.orchestrator_model)

        logger.debug("All agents initialized for Agno workflow")

    def _create_storage(self) -> SqliteStorage:
        """Create storage backend for the workflow."""
        return SqliteStorage(
            table_name="agno_workflow_sessions",
            db_file="tmp/agno_workflows.db",
        )

    def run(self, complaint: ComplaintCase) -> Iterator[RunResponse]:
        """Main workflow execution method.
        
        This is the core workflow logic implemented as a pure Python function,
        following Agno's recommended approach for maximum flexibility.
        
        Args:
            complaint: The complaint case to classify
            
        Yields:
            RunResponse objects with intermediate and final results
        """
        try:
            logger.info(f"Starting workflow classification for case: {complaint.case_id}")
            
            # Step 1: Main category analysis
            yield RunResponse(content="Starting main category analysis...")
            main_result = self._run_main_category_analysis(complaint)
            self.session_state["main_result"] = main_result.model_dump()
            self.session_state["complaint"] = complaint.model_dump()
            
            # Step 2: Sub-category analysis
            yield RunResponse(content="Starting sub-category analysis...")
            main_category = (
                main_result.candidates[0].category if main_result.candidates else None
            )
            if not main_category:
                raise ValueError("No main category candidates found")
                
            sub_result = self._run_sub_category_analysis(complaint, main_category)
            self.session_state["sub_result"] = sub_result.model_dump()
            
            # Step 3: Review analysis with retry logic
            yield RunResponse(content="Starting review analysis...")
            review_result = self._run_review_analysis(
                complaint, main_result, sub_result
            )
            self.session_state["review_result"] = review_result.model_dump()
            
            # Step 4: Output formatting
            yield RunResponse(content="Formatting final result...")
            final_result = self._run_output_formatting(
                complaint, main_result, sub_result, review_result
            )
            
            logger.info(f"Workflow classification completed: {final_result.category}")
            yield RunResponse(content=final_result.model_dump())
            
        except Exception as e:
            logger.error(f"Error in workflow execution: {e}")
            fallback_result = self._create_fallback_result(complaint, e)
            yield RunResponse(content=fallback_result.model_dump())

    def _run_async_in_sync(self, coro):
        """Helper to run async functions in sync context."""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Create new event loop for nested async call
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(lambda: asyncio.run(coro))
                    return future.result()
            else:
                return loop.run_until_complete(coro)
        except RuntimeError:
            # No event loop, create one
            return asyncio.run(coro)

    def _run_main_category_analysis(self, complaint: ComplaintCase) -> MainCategoryResult:
        """Execute main category analysis.

        Args:
            complaint: The complaint case to analyze

        Returns:
            Main category analysis result
        """
        try:
            logger.debug(f"Running main category analysis for case: {complaint.case_id}")
            
            main_result = self._run_async_in_sync(self.main_analyzer.analyze(complaint))
            
            logger.info(
                f"Main category analysis completed: {main_result.candidates[0].category if main_result.candidates else 'No candidates'}"
            )
            return main_result

        except Exception as e:
            logger.error(f"Error in main category analysis: {e}")
            raise

    def _run_sub_category_analysis(self, complaint: ComplaintCase, main_category) -> SubCategoryResult:
        """Execute sub-category analysis.

        Args:
            complaint: The complaint case to analyze
            main_category: The main category from previous analysis

        Returns:
            Sub-category analysis result
        """
        try:
            logger.debug(f"Running sub-category analysis for main category: {main_category.value}")
            
            sub_result = self._run_async_in_sync(
                self.sub_analyzer.analyze(complaint, main_category=main_category)
            )
            
            logger.info(f"Sub-category analysis completed: {sub_result.candidates[0].category if sub_result.candidates else 'No candidates'}")
            return sub_result

        except Exception as e:
            logger.error(f"Error in sub-category analysis: {e}")
            raise

    def _run_review_analysis(self, complaint: ComplaintCase, main_result: MainCategoryResult, sub_result: SubCategoryResult) -> ReviewResult:
        """Execute review analysis with retry logic.

        Args:
            complaint: The complaint case to review
            main_result: Main category analysis result
            sub_result: Sub-category analysis result

        Returns:
            Review analysis result
        """
        max_retries = 3
        for retry_count in range(max_retries):
            try:
                logger.debug(f"Running review analysis (attempt {retry_count + 1})")
                
                review_result = self._run_async_in_sync(
                    self.review_agent.analyze(complaint, main_result, sub_result)
                )
                
                # Check if review passed
                if review_result.decision == DecisionType.PASS:
                    logger.info("Review analysis passed")
                    return review_result
                else:
                    logger.warning(f"Review analysis failed (attempt {retry_count + 1}): {review_result.feedback}")
                    if retry_count < max_retries - 1:
                        continue  # Retry

            except Exception as e:
                logger.error(f"Error in review analysis (attempt {retry_count + 1}): {e}")
                if retry_count < max_retries - 1:
                    continue  # Retry

        # All retries failed, create a pass result to continue workflow
        logger.error(f"Review analysis failed after {max_retries} attempts, proceeding with PASS")
        return ReviewResult(
            decision=DecisionType.PASS,
            feedback="Review failed after maximum retries, proceeding with classification",
            confidence=0.5
        )

    def _run_output_formatting(self, complaint: ComplaintCase, main_result: MainCategoryResult, sub_result: SubCategoryResult, review_result: ReviewResult) -> ClassificationResult:
        """Execute output formatting.

        Args:
            complaint: The complaint case
            main_result: Main category analysis result
            sub_result: Sub-category analysis result
            review_result: Review analysis result

        Returns:
            Final classification result
        """
        try:
            logger.debug("Running output formatting")
            
            output_result = self._run_async_in_sync(
                self.output_agent.analyze(complaint, main_result, sub_result, review_result)
            )
            
            logger.info(f"Output formatting completed: {output_result.category}")
            return output_result

        except Exception as e:
            logger.error(f"Error in output formatting: {e}")
            raise

    def _create_fallback_result(self, complaint: ComplaintCase, error: Exception) -> ClassificationResult:
        """Create fallback classification result for errors.

        Args:
            complaint: The complaint case
            error: The error that occurred

        Returns:
            Fallback classification result
        """
        logger.warning(f"Creating fallback result due to error: {error}")
        
        return ClassificationResult(
            case_id=complaint.case_id,
            category=SubCategory.OTHER_GENERAL,
            confidence=0.1,
            reasoning="Fallback classification due to workflow error",
            processing_time=0.0,
            agent_used="fallback",
            error_message=str(error)
        )

    async def classify_complaint(self, complaint: ComplaintCase) -> ClassificationResult:
        """Main entry point for complaint classification.

        Args:
            complaint: The complaint case to classify

        Returns:
            Classification result
        """
        try:
            logger.info(f"Starting Agno workflow classification for case: {complaint.case_id}")

            # Run the workflow using the correct method
            responses = list(self.run(complaint))
            
            # Get the final response (last one should contain the result)
            if responses:
                final_response = responses[-1]
                if hasattr(final_response, 'content') and final_response.content:
                    if isinstance(final_response.content, dict):
                        result = ClassificationResult(**final_response.content)
                    else:
                        # If content is already a ClassificationResult
                        result = final_response.content
                    
                    logger.info(f"Agno workflow classification completed: {result.category}")
                    return result
                else:
                    raise ValueError("No valid response from workflow")
            else:
                raise ValueError("No responses from workflow")

        except Exception as e:
            logger.error(f"Error in Agno workflow classification: {e}")
            # Create fallback result
            return self._create_fallback_result(complaint, e)
