"""
Review Agent for quality assurance and validation.
"""

import logging

from agno.agent import Agent

from ..config import ModelConfig
from ..knowledge.context_injector import ContextInjector
from ..models import ComplaintCase, MainCategoryResult, ReviewResult, SubCategoryResult
from .base import BaseClassificationAgent

logger = logging.getLogger(__name__)


class ReviewAgent(BaseClassificationAgent):
    """
    Review Agent - Quality assurance and validation specialist.

    Responsibilities:
    - Critical thinking and quality assurance
    - Challenge the conclusions from previous agents
    - Verify logical consistency between main and sub categories
    - Provide independent validation of classification decisions
    """

    def __init__(self, model_config: ModelConfig, context_injector: ContextInjector):
        """Initialize the Review Agent.

        Args:
            model_config: Configuration for the AI model
            context_injector: Context injector for knowledge base access
        """
        super().__init__(
            model_config=model_config,
            name="Review Agent",
            role="Independent quality assurance and validation specialist",
            response_model=ReviewResult,
        )
        self.context_injector = context_injector

    def _get_system_instructions(self) -> list[str]:
        """Get system instructions for the Review Agent."""
        return [
            "你是一位獨立的稽核員，負責批判性思維與品質保證。",
            "你的任務是嚴格審查前序分類流程的完整性與邏輯一致性。",
            "你需要特別檢查子分類是否確實隸屬於主分類的業務範疇內。",
            "你要挑戰前序結論的合理性，確保分類決策的正確性。",
            "如果發現邏輯衝突或分類錯誤，你必須明確指出並建議改正。",
            "你必須以 JSON 格式回應，包含 decision ('pass' 或 'fail')、justification 和 recommendation。",
            "decision 為 'pass' 表示分類正確，'fail' 表示發現問題。",
            "justification 應該詳細說明你的審查結論和理由。",
            "recommendation 在 fail 情況下提供改善建議。",
        ]

    def _create_agent(self) -> Agent:
        """Create the Review Agent."""
        additional_instructions = [
            "回應格式範例：",
            '通過案例：{"decision": "pass", "justification": "主分類和子分類邏輯一致，分類決策合理。", "recommendation": null}',
            '失敗案例：{"decision": "fail", "justification": "邏輯衝突。主案類選擇了『路霸排除』，但子案類選擇了『交通號誌故障』。子案類根本不屬於該主案類。", "recommendation": "建議重新以『交通號誌...』為主案類進行分析。"}',
        ]

        return self._create_base_agent(additional_instructions)

    async def _process_internal(
        self,
        complaint: ComplaintCase,
        main_result: MainCategoryResult,
        sub_result: SubCategoryResult,
        **kwargs,
    ) -> ReviewResult:
        """Process complaint for review and validation.

        Args:
            complaint: The original complaint case
            main_result: Result from main category analysis
            sub_result: Result from sub-category analysis
            **kwargs: Additional arguments

        Returns:
            Review result with pass/fail decision
        """
        # Get the top candidates
        main_candidate = main_result.candidates[0] if main_result.candidates else None
        sub_candidate = sub_result.candidates[0] if sub_result.candidates else None

        if not main_candidate or not sub_candidate:
            return ReviewResult(
                decision="fail",
                justification="缺少主分類或子分類候選，無法進行審核。",
                recommendation="請確保主分類和子分類分析都有提供候選結果。",
            )

        # Get context for review
        context = self.context_injector.create_review_context(
            main_candidate.category, sub_candidate.category
        )

        # Create prompt with injected context
        prompt = self._create_review_prompt(
            complaint.content, main_candidate, sub_candidate, context
        )

        # Get response from agent with structured output
        response = await self.agent.arun(prompt)
        
        # Store the response for metrics tracking
        self._last_agent_response = response

        # With response_model, the response.content should already be a ReviewResult
        if isinstance(response.content, ReviewResult):
            return response.content

        # Fallback: try to create ReviewResult from response content
        try:
            if hasattr(response.content, "model_dump"):
                # If it's a Pydantic model, convert to dict first
                result_data = response.content.model_dump()
            elif isinstance(response.content, dict):
                result_data = response.content
            else:
                # Last resort: try to parse as JSON
                from ..utils import parse_json_response

                result_data = parse_json_response(str(response.content))

            if result_data:
                return ReviewResult(**result_data)
        except (TypeError, ValueError) as e:
            logger.error(f"Failed to create ReviewResult from response: {e}")

        # Fallback to fail if parsing fails
        logger.error(f"Failed to parse review response: {response.content}")
        return ReviewResult(
            decision="fail",
            justification="解析審核回應失敗",
            recommendation="請重新進行分類分析。",
        )

    def _create_review_prompt(
        self, complaint_content: str, main_candidate, sub_candidate, context
    ) -> str:
        """Create the prompt for review analysis.

        Args:
            complaint_content: The complaint text content
            main_candidate: Selected main category candidate
            sub_candidate: Selected sub-category candidate
            context: Context data with category definitions

        Returns:
            Formatted prompt string
        """
        # Format the category definitions for review
        definition_section = self.context_injector.format_review_prompt_section(context)

        return f"""你是一位獨立的稽核員。請嚴格審查以下分類流程的完整性與邏輯一致性，特別是檢查子分類是否確實隸屬於主分類的業務範疇內。

**原始案文:**
{complaint_content}

**主分類結論:**
- 分類: {main_candidate.category}
- 信心度: {main_candidate.confidence}
- 理由: {main_candidate.reasoning}

**子分類結論:**
- 分類: {sub_candidate.category}
- 信心度: {sub_candidate.confidence}
- 理由: {sub_candidate.reasoning}

{definition_section}

**審查任務:**
1. 檢查主分類選擇是否合理，是否符合案件的核心問題
2. 檢查子分類是否確實屬於所選主分類的業務範疇
3. 檢查分類邏輯是否一致，是否存在矛盾
4. 評估整體分類決策的合理性

**判斷標準:**
- 如果主分類和子分類邏輯一致，且分類決策合理，請回應 "pass"
- 如果發現邏輯衝突、分類錯誤或不合理之處，請回應 "fail"

請以 JSON 格式回應，包含 decision、justification 和 recommendation 欄位。"""
