"""
Batch processing and reporting functionality for usage tracking.
"""

import logging
import json
import csv
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from decimal import Decimal

from .tracker import UsageTracker, UsageSummary, TokenUsage

logger = logging.getLogger(__name__)


class BatchReporter:
    """Handles batch processing reports and cost analysis."""
    
    def __init__(self, usage_tracker: Optional[UsageTracker] = None):
        """Initialize the batch reporter.
        
        Args:
            usage_tracker: Usage tracker instance. If None, creates a new one.
        """
        self.usage_tracker = usage_tracker or UsageTracker()
        self.batch_results: List[Dict[str, Any]] = []
        self.batch_start_time: Optional[datetime] = None
        self.batch_end_time: Optional[datetime] = None
        
    def start_batch(self):
        """Start a new batch processing session."""
        self.usage_tracker.start_batch_tracking()
        self.batch_results = []
        self.batch_start_time = datetime.now()
        self.batch_end_time = None
        logger.info("Started batch processing session")
    
    def add_classification_result(
        self, 
        case_id: str, 
        content: str, 
        result: Any,
        processing_time: Optional[float] = None
    ):
        """Add a classification result to the batch.
        
        Args:
            case_id: Unique identifier for the case
            content: Original complaint content
            result: Classification result
            processing_time: Time taken to process this case
        """
        batch_item = {
            "case_id": case_id,
            "content": content[:200] + "..." if len(content) > 200 else content,  # Truncate long content
            "category": result.category.value if hasattr(result, 'category') else str(result),
            "main_category": result.main_category.value if hasattr(result, 'main_category') else "",
            "confidence": result.confidence if hasattr(result, 'confidence') else None,
            "processing_time": processing_time,
            "timestamp": datetime.now().isoformat(),
            "usage_summary": result.usage_summary.model_dump() if hasattr(result, 'usage_summary') and result.usage_summary else None
        }
        
        self.batch_results.append(batch_item)
        logger.debug(f"Added classification result for case: {case_id}")
    
    def end_batch(self) -> Dict[str, Any]:
        """End the batch processing session and return summary.
        
        Returns:
            Dictionary with batch processing summary
        """
        self.batch_end_time = datetime.now()
        batch_summary = self.usage_tracker.end_batch_tracking()
        
        if self.batch_start_time and self.batch_end_time:
            total_processing_time = (self.batch_end_time - self.batch_start_time).total_seconds()
        else:
            total_processing_time = None
        
        summary = {
            "batch_info": {
                "start_time": self.batch_start_time.isoformat() if self.batch_start_time else None,
                "end_time": self.batch_end_time.isoformat() if self.batch_end_time else None,
                "total_processing_time_seconds": total_processing_time,
                "total_cases": len(self.batch_results)
            },
            "usage_summary": {
                "total_requests": batch_summary.total_requests if batch_summary else 0,
                "total_input_tokens": batch_summary.total_input_tokens if batch_summary else 0,
                "total_output_tokens": batch_summary.total_output_tokens if batch_summary else 0,
                "total_tokens": batch_summary.total_tokens if batch_summary else 0,
                "total_cost_usd": str(batch_summary.total_cost) if batch_summary else "0",
                "model_breakdown": batch_summary.model_breakdown if batch_summary else {}
            },
            "results": self.batch_results
        }
        
        logger.info(f"Batch processing completed: {len(self.batch_results)} cases processed")
        if batch_summary:
            logger.info(f"Total cost: ${batch_summary.total_cost:.6f} USD")
        
        return summary
    
    def export_to_json(self, filepath: Union[str, Path], summary: Optional[Dict[str, Any]] = None) -> str:
        """Export batch results to JSON file.
        
        Args:
            filepath: Path to save the JSON file
            summary: Batch summary to export. If None, uses current session.
            
        Returns:
            Path to the exported file
        """
        if summary is None:
            summary = self.end_batch()
        
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Custom JSON encoder for Decimal
        class DecimalEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, Decimal):
                    return str(obj)
                return super().default(obj)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False, cls=DecimalEncoder)
        
        logger.info(f"Batch results exported to JSON: {filepath}")
        return str(filepath)
    
    def export_to_csv(self, filepath: Union[str, Path], summary: Optional[Dict[str, Any]] = None) -> str:
        """Export batch results to CSV file.
        
        Args:
            filepath: Path to save the CSV file
            summary: Batch summary to export. If None, uses current session.
            
        Returns:
            Path to the exported file
        """
        if summary is None:
            summary = self.end_batch()
        
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            if not summary["results"]:
                # Write empty CSV with headers
                writer = csv.writer(f)
                writer.writerow([
                    "case_id", "category", "main_category", "confidence", 
                    "processing_time", "timestamp", "input_tokens", "output_tokens", 
                    "total_tokens", "total_cost_usd"
                ])
                return str(filepath)
            
            # Get fieldnames from first result
            fieldnames = [
                "case_id", "category", "main_category", "confidence", 
                "processing_time", "timestamp", "input_tokens", "output_tokens", 
                "total_tokens", "total_cost_usd"
            ]
            
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in summary["results"]:
                usage_summary = result.get("usage_summary")
                row = {
                    "case_id": result["case_id"],
                    "category": result["category"],
                    "main_category": result["main_category"],
                    "confidence": result["confidence"],
                    "processing_time": result["processing_time"],
                    "timestamp": result["timestamp"],
                    "input_tokens": usage_summary.get("total_input_tokens", 0) if usage_summary else 0,
                    "output_tokens": usage_summary.get("total_output_tokens", 0) if usage_summary else 0,
                    "total_tokens": usage_summary.get("total_tokens", 0) if usage_summary else 0,
                    "total_cost_usd": usage_summary.get("total_cost", "0") if usage_summary else "0"
                }
                writer.writerow(row)
        
        logger.info(f"Batch results exported to CSV: {filepath}")
        return str(filepath)
    
    def generate_cost_analysis_report(self, summary: Optional[Dict[str, Any]] = None) -> str:
        """Generate a detailed cost analysis report.
        
        Args:
            summary: Batch summary to analyze. If None, uses current session.
            
        Returns:
            Formatted cost analysis report as string
        """
        if summary is None:
            summary = self.end_batch()
        
        batch_info = summary["batch_info"]
        usage_summary = summary["usage_summary"]
        results = summary["results"]
        
        # Calculate statistics
        total_cases = batch_info["total_cases"]
        total_cost = Decimal(usage_summary["total_cost_usd"])
        avg_cost_per_case = total_cost / total_cases if total_cases > 0 else Decimal("0")
        
        # Processing time statistics
        processing_times = [r["processing_time"] for r in results if r["processing_time"] is not None]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # Generate report
        report_lines = [
            "=" * 60,
            "📊 AI 陳情分類系統 - 批次處理成本分析報告",
            "=" * 60,
            "",
            "🕒 處理時間資訊:",
            f"   開始時間: {batch_info['start_time']}",
            f"   結束時間: {batch_info['end_time']}",
            f"   總處理時間: {batch_info['total_processing_time_seconds']:.2f} 秒" if batch_info['total_processing_time_seconds'] else "   總處理時間: 未知",
            f"   平均每案處理時間: {avg_processing_time:.2f} 秒" if avg_processing_time > 0 else "   平均每案處理時間: 未知",
            "",
            "📈 案件統計:",
            f"   總處理案件數: {total_cases:,}",
            f"   總 API 請求次數: {usage_summary['total_requests']:,}",
            "",
            "🤖 Token 使用統計:",
            f"   輸入 Token: {usage_summary['total_input_tokens']:,}",
            f"   輸出 Token: {usage_summary['total_output_tokens']:,}",
            f"   總 Token: {usage_summary['total_tokens']:,}",
            "",
            "💰 成本分析:",
            f"   總成本: ${total_cost:.6f} USD",
            f"   平均每案成本: ${avg_cost_per_case:.6f} USD",
            f"   每千 Token 平均成本: ${(total_cost / usage_summary['total_tokens'] * 1000):.6f} USD" if usage_summary['total_tokens'] > 0 else "   每千 Token 平均成本: N/A",
            ""
        ]
        
        # Model breakdown
        if usage_summary["model_breakdown"]:
            report_lines.extend([
                "🔧 模型使用分解:",
                ""
            ])
            
            for model, stats in usage_summary["model_breakdown"].items():
                model_cost = Decimal(str(stats["total_cost"]))
                cost_percentage = (model_cost / total_cost * 100) if total_cost > 0 else 0
                
                report_lines.extend([
                    f"   📱 {model}:",
                    f"      請求次數: {stats['requests']:,}",
                    f"      Token 使用: {stats['total_tokens']:,} (輸入: {stats['input_tokens']:,}, 輸出: {stats['output_tokens']:,})",
                    f"      成本: ${model_cost:.6f} USD ({cost_percentage:.1f}%)",
                    ""
                ])
        
        # Cost projections
        if total_cases >= 10:  # Only show projections for meaningful sample sizes
            report_lines.extend([
                "📊 成本預測 (基於當前批次數據):",
                f"   100 案件預估成本: ${(avg_cost_per_case * 100):.4f} USD",
                f"   500 案件預估成本: ${(avg_cost_per_case * 500):.4f} USD",
                f"   1000 案件預估成本: ${(avg_cost_per_case * 1000):.4f} USD",
                ""
            ])
        
        report_lines.extend([
            "=" * 60,
            f"報告生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "=" * 60
        ])
        
        return "\n".join(report_lines)