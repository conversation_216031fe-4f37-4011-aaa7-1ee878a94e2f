"""
Token usage tracking and cost calculation module.
"""

import logging
from dataclasses import dataclass, field
from decimal import ROUND_HALF_UP, Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class ModelProvider(str, Enum):
    """Supported model providers."""

    OPENAI = "openai"
    ANTHROPIC = "anthropic"


@dataclass
class TokenUsage:
    """Token usage information for a single API call."""

    model: str
    provider: ModelProvider
    input_tokens: int = 0
    output_tokens: int = 0
    total_tokens: int = 0

    # Additional token types from Agno metrics
    cached_tokens: int = 0
    reasoning_tokens: int = 0
    prompt_tokens: int = 0
    completion_tokens: int = 0

    # Cost information
    input_cost: Decimal = field(default_factory=lambda: Decimal("0"))
    output_cost: Decimal = field(default_factory=lambda: Decimal("0"))
    total_cost: Decimal = field(default_factory=lambda: Decimal("0"))

    # Timing information
    processing_time: Optional[float] = None

    @classmethod
    def from_agno_metrics(
        cls, model: str, provider: str, metrics: Dict[str, Any]
    ) -> "TokenUsage":
        """Create TokenUsage from Agno metrics dictionary."""
        return cls(
            model=model,
            provider=ModelProvider(provider.lower()),
            input_tokens=metrics.get("input_tokens", 0),
            output_tokens=metrics.get("output_tokens", 0),
            total_tokens=metrics.get("total_tokens", 0),
            cached_tokens=metrics.get("cached_tokens", 0),
            reasoning_tokens=metrics.get("reasoning_tokens", 0),
            prompt_tokens=metrics.get("prompt_tokens", 0),
            completion_tokens=metrics.get("completion_tokens", 0),
            processing_time=metrics.get("time"),
        )


@dataclass
class ModelPricing:
    """Pricing information for a model."""

    model_id: str
    provider: ModelProvider
    input_cost_per_million: Decimal  # Cost per 1M input tokens
    output_cost_per_million: Decimal  # Cost per 1M output tokens

    def calculate_cost(
        self, input_tokens: int, output_tokens: int
    ) -> tuple[Decimal, Decimal, Decimal]:
        """Calculate input, output, and total cost for given token counts."""
        input_cost = (
            Decimal(input_tokens) / Decimal("1000000")
        ) * self.input_cost_per_million
        output_cost = (
            Decimal(output_tokens) / Decimal("1000000")
        ) * self.output_cost_per_million
        total_cost = input_cost + output_cost

        # Round to 6 decimal places for precision
        input_cost = input_cost.quantize(Decimal("0.000001"), rounding=ROUND_HALF_UP)
        output_cost = output_cost.quantize(Decimal("0.000001"), rounding=ROUND_HALF_UP)
        total_cost = total_cost.quantize(Decimal("0.000001"), rounding=ROUND_HALF_UP)

        return input_cost, output_cost, total_cost


class CostCalculator:
    """Calculate costs for different models and providers."""

    def __init__(self):
        """Initialize with default pricing."""
        self.pricing: Dict[str, ModelPricing] = {}
        self._load_default_pricing()

    def _load_default_pricing(self):
        """Load default pricing for supported models."""
        # OpenAI pricing (as of the user's requirement)
        self.pricing["gpt-4.1-mini"] = ModelPricing(
            model_id="gpt-4.1-mini",
            provider=ModelProvider.OPENAI,
            input_cost_per_million=Decimal("0.40"),
            output_cost_per_million=Decimal("1.60"),
        )

        # Add more models as needed
        self.pricing["gpt-4o"] = ModelPricing(
            model_id="gpt-4o",
            provider=ModelProvider.OPENAI,
            input_cost_per_million=Decimal("5.00"),
            output_cost_per_million=Decimal("15.00"),
        )

        # Anthropic pricing (approximate)
        self.pricing["claude-3-haiku-20240307"] = ModelPricing(
            model_id="claude-3-haiku-20240307",
            provider=ModelProvider.ANTHROPIC,
            input_cost_per_million=Decimal("0.25"),
            output_cost_per_million=Decimal("1.25"),
        )

    def add_model_pricing(self, pricing: ModelPricing):
        """Add or update pricing for a model."""
        self.pricing[pricing.model_id] = pricing

    def calculate_cost(self, usage: TokenUsage) -> TokenUsage:
        """Calculate and set cost information for TokenUsage."""
        model_pricing = self.pricing.get(usage.model)

        if not model_pricing:
            logger.warning(
                f"No pricing found for model: {usage.model}, using default rates"
            )
            # Use gpt-4o-mini as default
            model_pricing = self.pricing["gpt-4o-mini"]

        # Calculate costs
        input_cost, output_cost, total_cost = model_pricing.calculate_cost(
            usage.input_tokens, usage.output_tokens
        )

        # Update usage object
        usage.input_cost = input_cost
        usage.output_cost = output_cost
        usage.total_cost = total_cost

        return usage


@dataclass
class UsageSummary:
    """Summary of token usage and costs."""

    total_requests: int = 0
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    total_tokens: int = 0
    total_cost: Decimal = field(default_factory=lambda: Decimal("0"))

    # Breakdown by model
    model_breakdown: Dict[str, Dict[str, Any]] = field(default_factory=dict)

    # Individual usage records
    usage_records: List[TokenUsage] = field(default_factory=list)

    def add_usage(self, usage: TokenUsage):
        """Add a usage record to the summary."""
        self.usage_records.append(usage)
        self.total_requests += 1
        self.total_input_tokens += usage.input_tokens
        self.total_output_tokens += usage.output_tokens
        self.total_tokens += usage.total_tokens
        self.total_cost += usage.total_cost

        # Update model breakdown
        if usage.model not in self.model_breakdown:
            self.model_breakdown[usage.model] = {
                "requests": 0,
                "input_tokens": 0,
                "output_tokens": 0,
                "total_tokens": 0,
                "total_cost": Decimal("0"),
            }

        breakdown = self.model_breakdown[usage.model]
        breakdown["requests"] += 1
        breakdown["input_tokens"] += usage.input_tokens
        breakdown["output_tokens"] += usage.output_tokens
        breakdown["total_tokens"] += usage.total_tokens
        breakdown["total_cost"] += usage.total_cost

    def get_formatted_summary(self) -> str:
        """Get a formatted string summary."""
        lines = [
            "🤖 Token 使用統計:",
            f"   總請求次數: {self.total_requests:,}",
            f"   輸入 Token: {self.total_input_tokens:,}",
            f"   輸出 Token: {self.total_output_tokens:,}",
            f"   總 Token: {self.total_tokens:,}",
            f"   總費用: ${self.total_cost:.6f} USD",
            "",
        ]

        if len(self.model_breakdown) > 1:
            lines.append("📊 模型分解:")
            for model, breakdown in self.model_breakdown.items():
                lines.extend(
                    [
                        f"   {model}:",
                        f"     請求次數: {breakdown['requests']:,}",
                        f"     輸入 Token: {breakdown['input_tokens']:,}",
                        f"     輸出 Token: {breakdown['output_tokens']:,}",
                        f"     費用: ${breakdown['total_cost']:.6f} USD",
                        "",
                    ]
                )

        return "\n".join(lines)


class UsageTracker:
    """Main usage tracking class."""

    def __init__(self):
        """Initialize the usage tracker."""
        self.cost_calculator = CostCalculator()
        self.session_summary = UsageSummary()
        self.batch_summary: Optional[UsageSummary] = None

        logger.info("UsageTracker initialized")

    def track_usage(
        self, model: str, provider: str, metrics: Dict[str, Any]
    ) -> TokenUsage:
        """Track usage from Agno metrics."""
        # Create usage from metrics
        usage = TokenUsage.from_agno_metrics(model, provider, metrics)

        # Calculate costs
        usage = self.cost_calculator.calculate_cost(usage)

        # Add to session summary
        self.session_summary.add_usage(usage)

        # Add to batch summary if active
        if self.batch_summary:
            self.batch_summary.add_usage(usage)

        logger.debug(
            f"Tracked usage for {model}: {usage.total_tokens} tokens, ${usage.total_cost} USD"
        )

        return usage

    def get_session_summary(self) -> UsageSummary:
        """Get current session usage summary."""
        return self.session_summary

    def start_batch_tracking(self):
        """Start tracking for a batch operation."""
        self.batch_summary = UsageSummary()
        logger.info("Started batch usage tracking")

    def get_batch_summary(self) -> Optional[UsageSummary]:
        """Get batch usage summary."""
        return self.batch_summary

    def end_batch_tracking(self) -> Optional[UsageSummary]:
        """End batch tracking and return summary."""
        summary = self.batch_summary
        self.batch_summary = None
        logger.info("Ended batch usage tracking")
        return summary

    def reset_session(self):
        """Reset session statistics."""
        self.session_summary = UsageSummary()
        logger.info("Reset session usage statistics")


# Global tracker instance
_global_tracker: Optional[UsageTracker] = None


def get_usage_tracker() -> UsageTracker:
    """Get the global usage tracker instance."""
    global _global_tracker
    if _global_tracker is None:
        _global_tracker = UsageTracker()
    return _global_tracker
