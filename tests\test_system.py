#!/usr/bin/env python3
"""
Simple test script to validate the complaint classification system.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from complaint_classifier import ComplaintClassifier
from complaint_classifier.config import Settings
from complaint_classifier.logging_config import setup_logging


async def test_system():
    """Test the complaint classification system."""

    # Setup logging first
    setup_logging(level="INFO")

    print("🤖 Testing AI Multi-Agent Complaint Classification System")
    print("=" * 60)

    try:
        # Initialize classifier
        print("📋 Initializing classifier...")
        settings = Settings()

        # Update knowledge base path to absolute path
        kb_path = Path(__file__).parent / "src" / "data" / "categories_def.json"
        settings.knowledge_base_path = str(kb_path)

        classifier = ComplaintClassifier(settings, setup_logs=False)  # Skip logging setup since we already did it
        print("✅ Classifier initialized successfully")

        # Test validation
        print("\n🔍 Validating system setup...")
        validation = await classifier.validate_setup()

        print("Validation Results:")
        for component, status in validation.items():
            status_icon = "✅" if status else "❌"
            print(f"  {status_icon} {component.replace('_', ' ').title()}")

        # Test getting categories
        print("\n📚 Testing knowledge base...")
        categories = classifier.get_available_categories()
        print(f"✅ Found {len(categories)} main categories")

        # Show first few categories
        for i, (main_cat, data) in enumerate(categories.items()):
            if i >= 3:  # Show only first 3
                break
            sub_count = len(data["sub_categories"])
            print(f"  📂 {main_cat} ({sub_count} sub-categories)")

        # Test statistics
        print("\n📊 System statistics...")
        stats = classifier.get_statistics()
        kb_stats = stats["knowledge_base"]
        print(f"  📚 Main categories: {kb_stats['main_categories']}")
        print(f"  📝 Sub-categories: {kb_stats['total_sub_categories']}")
        print(
            f"  ⚡ Fast track: {'Enabled' if stats['configuration']['fast_track_enabled'] else 'Disabled'}"
        )

        # Test classification (mock mode - no API calls)
        print("\n🧪 Testing classification (mock mode)...")

        # Mock the workflow engine to avoid API calls
        from complaint_classifier.models import ClassificationResult

        from complaint_classifier.models import SubCategory, MainCategory
        
        mock_result = ClassificationResult(
            category=SubCategory.STREET_LIGHT_MALFUNCTION,
            main_category=MainCategory.STREET_LIGHTS_TREES_PARKS,
            confidence=0.95,
            path_taken="fast_track",
            reasoning_chain=[
                {
                    "agent": "TriageAgent",
                    "metadata": {"step": "triage"},
                    "input": {"content": "路燈不亮"},
                    "output": {"action": "finalize", "category": "路燈維護"},
                }
            ],
            review_passed=None,
        )

        # Replace the classify method temporarily
        async def mock_classify(complaint):
            return mock_result

        original_classify = classifier.workflow_engine.classify_complaint
        classifier.workflow_engine.classify_complaint = mock_classify

        result = await classifier.classify(
            content="路燈不亮了，請派人修理", case_id="TEST001"
        )

        print("✅ Mock classification successful:")
        print(f"  📂 Category: {result.category}")
        print(f"  🎯 Confidence: {result.confidence}")
        print(f"  🛤️  Path: {result.path_taken}")
        print(f"  🧠 Reasoning steps: {len(result.reasoning_chain)}")

        # Restore original method
        classifier.workflow_engine.classify_complaint = original_classify

        print("\n🎉 All tests passed!")
        print("\nSystem is ready for use!")

        if not validation.get("configuration", False):
            print("\n⚠️  Note: No API keys configured.")
            print(
                "Set OPENAI_API_KEY or ANTHROPIC_API_KEY to enable real classification."
            )

        return True

    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_system())
    sys.exit(0 if success else 1)
