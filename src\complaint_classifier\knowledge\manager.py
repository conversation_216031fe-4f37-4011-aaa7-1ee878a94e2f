"""
Knowledge base manager for loading and managing category definitions.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Optional

from .models import CategoryDefinition, KnowledgeBase, SubCategoryDefinition

logger = logging.getLogger(__name__)


class KnowledgeManager:
    """Manages the knowledge base for complaint classification."""

    def __init__(self, knowledge_base_path: str):
        """Initialize the knowledge manager.

        Args:
            knowledge_base_path: Path to the knowledge base JSON file
        """
        self.knowledge_base_path = Path(knowledge_base_path)
        self._knowledge_base: Optional[KnowledgeBase] = None

    def load_knowledge_base(self) -> KnowledgeBase:
        """Load the knowledge base from JSON file.

        Returns:
            Loaded knowledge base

        Raises:
            FileNotFoundError: If knowledge base file doesn't exist
            ValueError: If JSON is invalid or malformed
        """
        if not self.knowledge_base_path.exists():
            raise FileNotFoundError(
                f"Knowledge base file not found: {self.knowledge_base_path}"
            )

        try:
            with open(self.knowledge_base_path, "r", encoding="utf-8") as f:
                raw_data = json.load(f)

            # Convert raw JSON to structured models
            categories = {}
            for main_cat_name, main_cat_data in raw_data.items():
                sub_categories = {}
                for sub_cat_name, sub_cat_data in main_cat_data.get(
                    "sub_categories", {}
                ).items():
                    sub_categories[sub_cat_name] = SubCategoryDefinition(
                        description=sub_cat_data["description"]
                    )

                categories[main_cat_name] = CategoryDefinition(
                    description=main_cat_data["description"],
                    sub_categories=sub_categories,
                )

            self._knowledge_base = KnowledgeBase(categories=categories)
            logger.info(f"Loaded knowledge base with {len(categories)} main categories")

            return self._knowledge_base

        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in knowledge base file: {e}")
        except KeyError as e:
            raise ValueError(f"Missing required field in knowledge base: {e}")
        except Exception:
            raise ValueError(f"Unknown error while loading knowledge base: {e}")

    @property
    def knowledge_base(self) -> KnowledgeBase:
        """Get the loaded knowledge base, loading it if necessary."""
        if self._knowledge_base is None:
            self.load_knowledge_base()
        return self._knowledge_base

    def get_main_category_definitions(self) -> Dict[str, str]:
        """Get all main category names and their descriptions.

        Returns:
            Dictionary mapping main category names to descriptions
        """
        kb = self.knowledge_base
        return {name: category.description for name, category in kb.categories.items()}

    def get_sub_category_definitions(self, main_category: str) -> Dict[str, str]:
        """Get sub-category definitions for a specific main category.

        Args:
            main_category: Name of the main category

        Returns:
            Dictionary mapping sub-category names to descriptions
        """
        kb = self.knowledge_base
        if main_category not in kb.categories:
            return {}

        return {
            name: sub_cat.description
            for name, sub_cat in kb.categories[main_category].sub_categories.items()
        }

    def validate_classification(self, main_category: str, sub_category: str) -> bool:
        """Validate that a classification is valid.

        Args:
            main_category: Main category name
            sub_category: Sub-category name

        Returns:
            True if the classification is valid, False otherwise
        """
        return self.knowledge_base.validate_category_path(main_category, sub_category)

    def get_statistics(self) -> Dict[str, int]:
        """Get statistics about the knowledge base.

        Returns:
            Dictionary with statistics
        """
        kb = self.knowledge_base
        total_sub_categories = sum(
            len(category.sub_categories) for category in kb.categories.values()
        )

        return {
            "main_categories": len(kb.categories),
            "total_sub_categories": total_sub_categories,
            "avg_sub_categories_per_main": round(
                total_sub_categories / len(kb.categories), 2
            ),
        }
