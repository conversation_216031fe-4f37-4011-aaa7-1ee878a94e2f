#!/usr/bin/env python3
"""Debug script to test the actual classification flow."""

import asyncio
import json
import logging
from dotenv import load_dotenv

from src.complaint_classifier.config import Settings
from src.complaint_classifier.agents.triage import TriageAgent
from src.complaint_classifier.models import ComplaintCase

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

load_dotenv()

async def debug_triage():
    """Debug the triage agent specifically."""
    try:
        # Initialize settings and triage agent
        settings = Settings()
        triage_agent = TriageAgent(settings.agents.triage_model)
        
        # Create a test complaint
        complaint = ComplaintCase(
            content="建議增設紅線，避免路肩被機車或汽車或雜物佔用，並且可以增加行人通行的安全性與夜間垃圾車與資源回收車進出的便利性",
            case_id="test-001"
        )
        
        print(f"Testing complaint: {complaint.content}")
        print(f"Model config: {settings.agents.triage_model}")
        
        # Create the prompt manually to see what's being sent
        prompt = triage_agent._create_triage_prompt(complaint.content)
        print(f"\nPrompt being sent:\n{prompt}")
        
        # Get raw response from agent
        print("\nGetting response from agent...")
        response = await triage_agent.agent.arun(prompt)
        
        print(f"\nRaw response type: {type(response)}")
        print(f"Response content type: {type(response.content)}")
        print(f"Response content length: {len(response.content) if response.content else 0}")
        print(f"Response content: '{response.content}'")
        print(f"Response content repr: {repr(response.content)}")
        
        # Try to parse JSON
        if response.content:
            try:
                result_data = json.loads(response.content)
                print(f"\nSuccessfully parsed JSON: {result_data}")
            except json.JSONDecodeError as e:
                print(f"\nJSON parsing failed: {e}")
                print(f"Content that failed to parse: '{response.content}'")
        else:
            print("\nResponse content is empty!")
        
        # Now test the full process method
        print("\n" + "="*50)
        print("Testing full triage process...")
        result = await triage_agent.process(complaint)
        print(f"Triage result: {result}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

async def debug_api_key():
    """Debug API key configuration."""
    settings = Settings()
    print(f"OpenAI API Key configured: {bool(settings.openai_api_key)}")
    if settings.openai_api_key:
        print(f"API Key starts with: {settings.openai_api_key[:10]}...")
    else:
        print("No OpenAI API key found!")

if __name__ == "__main__":
    print("=== API Key Debug ===")
    asyncio.run(debug_api_key())
    
    print("\n=== Triage Debug ===")
    asyncio.run(debug_triage())
